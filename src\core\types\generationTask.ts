// 生成任务相关的类型定义

export enum TaskStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

export enum ProcessingStage {
  INIT = 'INIT',
  PHOTO_ANALYSIS = 'PHOTO_ANALYSIS',
  KEYWORD_GENERATION = 'KEYWORD_GENERATION',
  VECTOR_SEARCH = 'VECTOR_SEARCH',
  MCP_FETCH = 'MCP_FETCH',
  CONTENT_GENERATION = 'CONTENT_GENERATION',
  FINAL_GENERATION = 'FINAL_GENERATION',
}

export interface IGenerationTask {
  taskId: string; // 主键 - 任务唯一标识
  userId: string; // 用户ID
  personaId: string; // 人设ID
  status: TaskStatus; // 任务状态
  stage: ProcessingStage; // 当前处理阶段
  selectedPhotos: string[]; // 选中的照片ID列表
  searchKeywords: string[]; // AI生成的搜索关键词
  topNResults: any[]; // 向量搜索结果
  mcpData: any[]; // 外部MCP数据

  candidateContent: any[]; // 候选内容
  finalContent: any[]; // 最终生成内容
  thinkingContent: string; // 思考过程
  errorMessage?: string; // 错误信息
  progress: number; // 进度百分比 0-100
  estimatedTimeRemaining?: number; // 预估剩余时间(秒)
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface TaskStatusResponse {
  taskId: string;
  status: TaskStatus;
  stage: ProcessingStage;
  progress: number;
  estimatedTimeRemaining?: number;
  errorMessage?: string;
}

export interface TaskResultResponse {
  taskId: string;
  finalContent: any[];
  candidateContent: any[];
  metadata: {
    usedPhotos: string[];
    keywords: string[];
    sources: any[];
  };
}

// 队列消息类型定义
export interface GenerateTaskMessage {
  taskId: string;
  userId: string;
  personaId: string;
  retryCount?: number;
  connectionId: string;
}

export interface PhotoAnalysisMessage {
  taskId: string;
  photoIds: string[];
  userId: string;
  personaId: string;
}

export interface KeywordGenerationMessage {
  taskId: string;
  userId: string;
  personaId: string;
  photosWithSummary: any[];
  persona: any;
  historyContent: string;
  connectionId: string;
}

export interface VectorSearchMessage {
  taskId: string;
  keywords: string[];
  userId: string;
  personaId: string;
  persona: any;
  selectedPhotos: any[];
  connectionId: string;
}
// taskId, userId, personaId, topNResults, mcpData, candidateContent, persona
export interface ContentGenerationMessage {
  taskId: string;
  searchResults: any[];
  mcpData?: any[];
  userId: string;
  topNResults?: any[];
  personaId: string;
  candidateContent?: any[];
  persona: any;
  selectedPhotos: any[];
  connectionId: string;
}

// WebSocket 消息类型
export interface WebSocketMessage {
  type: 'TASK_STATUS_UPDATE' | 'TASK_COMPLETED' | 'TASK_FAILED';
  taskId: string;
  data: any;
}

// 任务创建请求
export interface CreateTaskRequest {
  personaId: string;
  selectedPhotos?: string[];
  preferences?: {
    contentType: string;
    style: string;
    length: number;
  };
}

// DataMergeMessage
export interface DataMergeMessage {
  taskId: string;
  userId: string;
  personaId: string;
  topNResults: any[];
  mcpData: any[];
  persona: any;
  selectedPhotos: any[];
  connectionId: string;
}
