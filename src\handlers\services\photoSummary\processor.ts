import 'dotenv/config';
import { SQSEvent, SQSRecord } from 'aws-lambda';
import { updatePhotoSummary } from '@lib/photo';
import { generatePhotoSummary } from '@services/ai/photoAnalysis';
import { genReadSignedUrl } from '@shared/utils/s3';
import { log } from '@shared/utils/structuredLogger';
import { deleteMessage } from '@infrastructure/messaging/sqs';

interface PhotoSummaryTask {
  photoId: string;
  s3Key: string;
  userId: string;
  fileName: string;
  lang: string;
}

/**
 * 处理单个照片摘要生成任务
 * @param task 任务信息
 */
async function processPhotoSummaryTask(task: PhotoSummaryTask): Promise<void> {
  const { photoId, s3Key, userId, fileName, lang } = task;

  try {
    log.businessEvent('PHOTO_SUMMARY_GENERATION_STARTED', userId, {
      photoId,
      fileName,
    });

    // 生成预签名URL用于OpenAI分析（如果需要）
    const bucketName = process.env.XHS_SOURCE_BUCKET_NAME!;
    const imageUrl = await genReadSignedUrl(bucketName, s3Key, 3600); // 1小时有效期

    // 使用AI生成摘要（Bedrock 直接使用 s3Key，OpenAI 使用 imageUrl）
    const summary = await generatePhotoSummary(imageUrl, s3Key, 'bedrock', lang);

    // 更新数据库中的摘要
    await updatePhotoSummary(photoId, summary);

    log.businessEvent('PHOTO_SUMMARY_GENERATION_SUCCESS', userId, {
      photoId,
      fileName,
      summaryLength: summary.length,
    });

    console.log(`照片摘要生成成功: ${photoId} - ${summary}`);
  } catch (error) {
    log.error('Photo summary generation failed', error as Error, {
      photoId,
      userId,
      fileName,
    });

    // 更新为错误状态的摘要
    try {
      await updatePhotoSummary(photoId, '摘要生成失败，请稍后重试');
    } catch (updateError) {
      console.error('Failed to update photo summary with error message:', updateError);
    }
  }
}

/**
 * SQS 事件处理器 - 批量处理照片摘要生成任务
 * @param event SQS事件
 */
export async function handler(event: SQSEvent): Promise<void> {
  console.log(`Processing ${event.Records.length} photo summary tasks`);

  // 并行处理所有任务
  const results = await Promise.allSettled(
    event.Records.map(async (record: SQSRecord) => {
      try {
        // 删除 SQS 消息
        try {
          await deleteMessage(process.env.PHOTO_SUMMARY_QUEUE_URL!, record.receiptHandle);
        } catch (error) {
          console.error('Error deleting SQS message:', error);
        }

        const task: PhotoSummaryTask = JSON.parse(record.body);
        await processPhotoSummaryTask(task);
        return { success: true, photoId: task.photoId };
      } catch (error) {
        console.error('Failed to process SQS record:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
      }
    }),
  );

  // 统计处理结果
  const successful = results.filter(
    (result) => result.status === 'fulfilled' && result.value.success,
  ).length;

  const failed = results.length - successful;

  console.log(`Photo summary processing completed: ${successful} successful, ${failed} failed`);

  if (failed > 0) {
    console.warn(
      'Some photo summary tasks failed:',
      results
        .filter((result) => result.status === 'rejected' || !result.value.success)
        .map((result) => (result.status === 'rejected' ? result.reason : result.value)),
    );
  }
}
