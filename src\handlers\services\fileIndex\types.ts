/**
 * 文件索引处理相关类型定义
 */

export interface FileIndexPayload {
  fileId: string;
  userId: string;
  personaId: string;
  s3Key: string;
  fileName: string;
}

export interface ProcessingConfig {
  minTextLength: number;
  embeddingBatchSize: number;
  embeddingParallelCount: number;
}

export interface ProcessingResult {
  totalPages: number;
  validPages: number;
  vectorCount: number;
  imageGroups: number;
}

export interface FileProcessingContext {
  fileId: string;
  userId: string;
  personaId: string;
  s3Key: string;
  fileName: string;
  config: ProcessingConfig;
}
