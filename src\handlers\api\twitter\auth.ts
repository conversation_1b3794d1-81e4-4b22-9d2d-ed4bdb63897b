import { APIGatewayEvent, Hand<PERSON> } from 'aws-lambda';

import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { getDBClient } from '@lib/init';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { checkAuth } from '@shared/utils/auth';
import { getRequestId } from '@shared/utils/requestUtils';
import { ResponseWrapper } from '@shared/utils/response';
import { TwitterOAuthService } from '@shared/utils/twitter/auth';

const dbClient = getDBClient();
const TEMP_AUTH_TABLE = 'XHS_TWITTER_AUTH_SESSIONS';

const twitterAuthHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    // 初始化Twitter OAuth服务
    const twitterOAuth = new TwitterOAuthService({
      clientId: process.env.TWITTER_CLIENT_ID!,
      clientSecret: process.env.TWITTER_CLIENT_SECRET!,
      redirectUri: process.env.TWITTER_REDIRECT_URI!,
      scopes: ['tweet.read', 'tweet.write', 'users.read', 'offline.access'],
    });

    // 生成授权URL
    const authFlow = twitterOAuth.generateAuthUrl();

    // 在DynamoDB中存储临时授权数据
    await dbClient.send(
      new PutCommand({
        TableName: TEMP_AUTH_TABLE,
        Item: {
          sessionId: authFlow.state,
          userId,
          codeVerifier: authFlow.codeVerifier,
          state: authFlow.state,
          expiresAt: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10分钟过期
          createdAt: new Date().toISOString(),
        },
      }),
    );

    return ResponseWrapper.success(
      {
        authUrl: authFlow.authUrl,
        state: authFlow.state,
      },
      requestId,
    );
  } catch (error) {
    console.error('Twitter auth error:', error);
    return ResponseWrapper.error(
      'INTERNAL_ERROR' as any,
      '创建Twitter授权链接失败',
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(twitterAuthHandler);
