import { PutCommand, QueryCommand, UpdateCommand, GetCommand } from '@aws-sdk/lib-dynamodb';
import { IGenerationTask, TaskStatus, ProcessingStage } from '@core/types/generationTask';
import { getDBClient } from './init';

const dbClient = getDBClient();
const TABLE_NAME = 'XHS_GENERATION_TASKS';

/**
 * 创建生成任务
 * @param data 任务数据
 */
export async function createGenerationTask(data: IGenerationTask): Promise<void> {
  try {
    const command = new PutCommand({
      TableName: TABLE_NAME,
      Item: {
        ...data,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt || new Date().toISOString(),
      },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('创建生成任务失败', error);
    throw error;
  }
}

/**
 * 根据任务ID获取任务
 * @param taskId 任务ID
 */
export async function getGenerationTask(taskId: string): Promise<IGenerationTask | null> {
  try {
    const command = new GetCommand({
      TableName: TABLE_NAME,
      Key: { taskId },
    });
    const result = await dbClient.send(command);
    return (result.Item as IGenerationTask) || null;
  } catch (error) {
    console.error('获取生成任务失败', error);
    throw error;
  }
}

/**
 * 更新任务状态
 * @param taskId 任务ID
 * @param updates 更新数据
 */
export async function updateGenerationTask(
  taskId: string,
  updates: Partial<IGenerationTask>,
): Promise<void> {
  try {
    const updateExpression: string[] = [];
    const expressionAttributeNames: Record<string, string> = {};
    const expressionAttributeValues: Record<string, any> = {};

    // 动态构建更新表达式
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        updateExpression.push(`#${key} = :${key}`);
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributeValues[`:${key}`] = value;
      }
    });

    // 总是更新 updatedAt
    updateExpression.push('#updatedAt = :updatedAt');
    expressionAttributeNames['#updatedAt'] = 'updatedAt';
    expressionAttributeValues[':updatedAt'] = new Date().toISOString();

    const command = new UpdateCommand({
      TableName: TABLE_NAME,
      Key: { taskId },
      UpdateExpression: `SET ${updateExpression.join(', ')}`,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
    });

    await dbClient.send(command);
  } catch (error) {
    console.error('更新生成任务失败', error);
    throw error;
  }
}

/**
 * 根据用户ID获取任务列表（返回全部数据）
 * @param userId 用户ID
 * @returns 任务列表
 */
/* eslint-disable no-await-in-loop */
export async function getTasksByUserId(userId: string): Promise<IGenerationTask[]> {
  try {
    const allTasks: IGenerationTask[] = [];
    let currentLastEvaluatedKey: any;

    // 循环查询直到获取所有数据
    do {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        IndexName: 'userId-createdAt-index', // 需要创建GSI
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: {
          ':userId': userId,
        },
        ExclusiveStartKey: currentLastEvaluatedKey,
        ScanIndexForward: false, // 按创建时间倒序
      });

      // 需要顺序执行以正确处理分页
      const result = await dbClient.send(command);
      const tasks = (result.Items as IGenerationTask[]) || [];

      allTasks.push(...tasks);
      currentLastEvaluatedKey = result.LastEvaluatedKey;
    } while (currentLastEvaluatedKey);

    return allTasks;
  } catch (error) {
    console.error('获取用户任务列表失败', error);
    throw error;
  }
}

/**
 * 根据状态获取任务列表（返回全部数据）
 * @param status 任务状态
 * @returns 任务列表
 */
/* eslint-disable no-await-in-loop */
export async function getTasksByStatus(status: TaskStatus): Promise<IGenerationTask[]> {
  try {
    const allTasks: IGenerationTask[] = [];
    let currentLastEvaluatedKey: any;

    // 循环查询直到获取所有数据
    do {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        IndexName: 'status-createdAt-index', // 需要创建GSI
        KeyConditionExpression: '#status = :status',
        ExpressionAttributeNames: {
          '#status': 'status',
        },
        ExpressionAttributeValues: {
          ':status': status,
        },
        ExclusiveStartKey: currentLastEvaluatedKey,
        ScanIndexForward: false, // 按创建时间倒序
      });

      // 需要顺序执行以正确处理分页
      const result = await dbClient.send(command);
      const tasks = (result.Items as IGenerationTask[]) || [];

      allTasks.push(...tasks);
      currentLastEvaluatedKey = result.LastEvaluatedKey;
    } while (currentLastEvaluatedKey);

    return allTasks;
  } catch (error) {
    console.error('根据状态获取任务列表失败', error);
    throw error;
  }
}

/**
 * 更新任务状态和阶段
 * @param taskId 任务ID
 * @param status 任务状态
 * @param stage 处理阶段
 * @param progress 进度
 * @param additionalData 额外数据
 */
export async function updateTaskStatus(
  taskId: string,
  status: TaskStatus,
  stage?: ProcessingStage,
  progress?: number,
  additionalData?: Partial<IGenerationTask>,
): Promise<void> {
  const updates: Partial<IGenerationTask> = {
    status,
    ...additionalData,
  };

  if (stage !== undefined) {
    updates.stage = stage;
  }

  if (progress !== undefined) {
    updates.progress = progress;
  }

  if (status === TaskStatus.COMPLETED) {
    updates.completedAt = new Date().toISOString();
  }

  await updateGenerationTask(taskId, updates);
}

/**
 * 获取超时任务
 * @param timeoutMinutes 超时分钟数
 */
export async function getTimeoutTasks(timeoutMinutes: number = 30): Promise<IGenerationTask[]> {
  try {
    const timeoutThreshold = new Date(Date.now() - timeoutMinutes * 60 * 1000).toISOString();

    const command = new QueryCommand({
      TableName: TABLE_NAME,
      IndexName: 'status-createdAt-index',
      KeyConditionExpression: '#status = :status',
      FilterExpression: 'createdAt < :timeoutThreshold',
      ExpressionAttributeNames: {
        '#status': 'status',
      },
      ExpressionAttributeValues: {
        ':status': TaskStatus.PROCESSING,
        ':timeoutThreshold': timeoutThreshold,
      },
    });

    const result = await dbClient.send(command);
    return (result.Items as IGenerationTask[]) || [];
  } catch (error) {
    console.error('获取超时任务失败', error);
    throw error;
  }
}

/**
 * 删除任务
 * @param taskId 任务ID
 */
export async function deleteGenerationTask(taskId: string): Promise<void> {
  try {
    const command = new UpdateCommand({
      TableName: TABLE_NAME,
      Key: { taskId },
      UpdateExpression: 'SET #status = :status, #updatedAt = :updatedAt',
      ExpressionAttributeNames: {
        '#status': 'status',
        '#updatedAt': 'updatedAt',
      },
      ExpressionAttributeValues: {
        ':status': TaskStatus.FAILED,
        ':updatedAt': new Date().toISOString(),
      },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('删除生成任务失败', error);
    throw error;
  }
}

/**
 * 获取生成任务的内容统计信息
 * @param userId 用户ID
 * @param personaId 人设ID
 * @returns 统计信息
 */
export async function getGenerationTaskContentStatistics(
  userId: string,
  personaId: string,
): Promise<{
  totalGenerated: number;
  publishedCount: number;
  favoritesCount: number;
  initCount: number;
  deletedCount: number;
}> {
  try {
    let allTasks: IGenerationTask[] = [];
    let lastEvaluatedKey: any;
    let hasMore = true;

    // 分页获取用户的所有已完成任务
    while (hasMore) {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        IndexName: 'userId-createdAt-index',
        KeyConditionExpression: 'userId = :userId',
        FilterExpression: 'personaId = :personaId AND #status = :status',
        ExpressionAttributeNames: {
          '#status': 'status',
        },
        ExpressionAttributeValues: {
          ':userId': userId,
          ':personaId': personaId,
          ':status': TaskStatus.COMPLETED,
        },
        ExclusiveStartKey: lastEvaluatedKey,
        Limit: 100, // 每次查询100个任务
      });

      // eslint-disable-next-line no-await-in-loop
      const result = await dbClient.send(command);
      const tasks = (result.Items as IGenerationTask[]) || [];
      allTasks = allTasks.concat(tasks);

      lastEvaluatedKey = result.LastEvaluatedKey;
      hasMore = !!lastEvaluatedKey;
    }

    // 统计所有finalContent中的内容
    let totalGenerated = 0;
    let publishedCount = 0;
    let favoritesCount = 0;
    let initCount = 0;
    let deletedCount = 0;

    allTasks.forEach((task) => {
      if (task.finalContent && Array.isArray(task.finalContent)) {
        task.finalContent.forEach((content: any) => {
          totalGenerated += 1; // 每个finalContent项都是模型生成的内容

          // 根据status统计各种状态
          switch (content.status) {
            case 'PUBLISHED':
              publishedCount += 1;
              break;
            case 'FAVORITES':
              favoritesCount += 1;
              break;
            case 'INIT':
              initCount += 1;
              break;
            case 'DELETED':
              deletedCount += 1;
              break;
            default:
              // 如果没有status或者是其他状态，默认为INIT
              initCount += 1;
              break;
          }
        });
      }
    });

    return {
      totalGenerated,
      publishedCount,
      favoritesCount,
      initCount,
      deletedCount,
    };
  } catch (error) {
    console.error('获取生成任务内容统计信息失败', error);
    throw error;
  }
}
