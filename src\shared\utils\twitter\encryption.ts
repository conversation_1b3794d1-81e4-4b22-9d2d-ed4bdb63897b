import crypto from 'crypto';

/**
 * Encryption service for Twitter tokens
 */
export class EncryptionService {
  private readonly algorithm = 'aes-256-gcm';

  private readonly secretKey: Buffer;

  constructor() {
    const keyString =
      process.env.ENCRYPTION_SECRET_KEY || 'default-secret-key-change-in-production';

    // Create a 32-byte key from the string
    this.secretKey = crypto.createHash('sha256').update(keyString).digest();
  }

  /**
   * Encrypt a string value
   * @param text Text to encrypt
   * @returns Encrypted string with IV and auth tag
   */
  async encrypt(text: string): Promise<string> {
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv(this.algorithm, this.secretKey, iv);

      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      const authTag = cipher.getAuthTag();

      // Combine IV, auth tag, and encrypted data
      const combined = `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
      return combined;
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt an encrypted string
   * @param encryptedData Encrypted string with IV and auth tag
   * @returns Decrypted string
   */
  async decrypt(encryptedData: string): Promise<string> {
    try {
      const parts = encryptedData.split(':');
      if (parts.length !== 3) {
        throw new Error('Invalid encrypted data format');
      }

      const iv = Buffer.from(parts[0], 'hex');
      const authTag = Buffer.from(parts[1], 'hex');
      const encrypted = parts[2];

      const decipher = crypto.createDecipheriv(this.algorithm, this.secretKey, iv);
      decipher.setAuthTag(authTag);

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt data');
    }
  }
}
