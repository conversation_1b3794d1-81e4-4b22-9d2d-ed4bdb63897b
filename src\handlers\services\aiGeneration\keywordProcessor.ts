// 关键词和选照片队列

import 'dotenv/config';
import { SQSEvent, SQSRecord } from 'aws-lambda';
import { KeywordGenerationMessage, ProcessingStage, TaskStatus } from '@core/types/generationTask';
import { deleteMessage, sendMessage } from '@infrastructure/messaging/sqs';
import { log } from '@shared/utils/structuredLogger';
import { updateTaskStatus } from '@infrastructure/database/db/generationTasks';
import { generatePhotoKeywords } from './ai/ai-photo-keyword';

/**
 * 处理单个关键词生成任务
 * @param task 任务信息
 */
async function processKeywordGenerationTask(task: KeywordGenerationMessage): Promise<void> {
  const { taskId, userId, personaId, photosWithSummary, persona, historyContent, connectionId } =
    task;
  try {
    log.businessEvent('GENERATION_TASK_KEYWORD_GENERATION_STARTED', userId, {
      taskId,
      photoCount: photosWithSummary.length,
    });
    const { keywords, selectedPhotos } = await generatePhotoKeywords(
      photosWithSummary,
      persona,
      historyContent,
    );
    // 更新数据
    await updateTaskStatus(taskId, TaskStatus.PROCESSING, ProcessingStage.VECTOR_SEARCH, 60, {
      searchKeywords: keywords,
      selectedPhotos: selectedPhotos.map((photo) => photo.id),
    });

    // 放进向量搜索队列
    await sendMessage(
      JSON.stringify({
        taskId,
        userId,
        personaId,
        keywords,
        persona,
        selectedPhotos,
        connectionId,
      }),
      process.env.VECTOR_SEARCH_QUEUE_URL!,
    );

    log.businessEvent('GENERATION_TASK_KEYWORD_GENERATION_SUCCESS', userId, {
      taskId,
      keywordCount: keywords.length,
    });
  } catch (error) {
    log.error('处理关键词生成任务失败', error as Error, {
      taskId,
      userId,
      personaId,
    });
  }
}

/**
 * SQS 事件处理器 - 处理关键词生成任务
 * @param event SQS事件
 */
export async function handler(event: SQSEvent): Promise<void> {
  console.log(`Processing ${event.Records.length} keyword generation tasks`);

  // 并行处理所有任务
  const results = await Promise.allSettled(
    event.Records.map(async (record: SQSRecord) => {
      try {
        // 删除 SQS 消息
        try {
          await deleteMessage(process.env.KEYWORD_GENERATION_QUEUE_URL!, record.receiptHandle);
        } catch (error) {
          console.error('Error deleting SQS message:', error);
        }

        const task: KeywordGenerationMessage = JSON.parse(record.body);
        await processKeywordGenerationTask(task);
        return { success: true, taskId: task.taskId };
      } catch (error) {
        console.error('Failed to process SQS record:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    }),
  );

  // 统计处理结果
  const successful = results.filter(
    (result) => result.status === 'fulfilled' && result.value.success,
  ).length;
  const failed = results.length - successful;
  console.log(
    `Keyword generation processing completed: ${successful} successful, ${failed} failed`,
  );

  if (failed > 0) {
    console.error(
      'Some keyword generation tasks failed:',
      results
        .filter((result) => result.status === 'fulfilled' && !result.value.success)
        .map((result) => (result.status === 'fulfilled' ? result.value.error : 'Unknown error')),
    );
  }
}
