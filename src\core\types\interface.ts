import { SubscriptionStatus } from '@paddle/paddle-node-sdk';

import { StatusEnum } from '@constants/enum';

export interface IPdf {
  pdfId: string;
  createdAt: number;
  fileName: string;
  s3Key: string;
  status: StatusEnum;
  totalPage: number;
  imgStatus: StatusEnum;
  workspaceId: string;
}

export interface IPdfPage {
  id: string;
  pdfId: string;
  page: number;
  content: string;
  s3Key?: string;
  userId: string;
  workspaceId: string;
  embedding?: number[];
  summary?: string;
}

export interface ISearchResult {
  id: string;
  pdfId: string;
  page: number;
  content: string;
}

export interface ISearchHistory {
  searchId: string;
  results: ISearchResult[];
  query: string;
}

export interface IWorkspace {
  id: string;
  userId: string;
  name: string;
  description?: string;
  s3key?: string; // 保存workspace封面图片的url
  pdfCount: number;
  isDefault?: boolean;
  cover?: string;
  createdAt?: number;
  sort?: number;
}

export interface ISubscription {
  subscriptionId: string;
  status: SubscriptionStatus;
  userId: string;
  createdAt: number;
  priceId: string;
  quantity: number;
  updatedAt: number;
  startedAt: number;
  curPeriodStartsAt: number | null;
  curPeriodEndsAt: number | null;
  nextBilledAt: number | null;
  canceledAt: number | null;
  scheduledChangeAction: string | null;
  scheduledChangeEffectiveAt: number | null;
}

export interface ITransaction {
  transactionId: string;
  status: string;
  userId: string;
  createdAt: number;
  priceCurrency: string;
  priceTotal: string;
  priceTax: string;
  priceId: string;
  updatedAt: number;
  subscriptionId?: string;
  paymentMethod?: string;
}

// 用户
export interface IUser {
  userId: string; // 用户唯一标识
  email?: string; // 邮箱地址
  phone?: string; // 手机号
  username?: string; // 用户名
  name?: string; // 用户姓名
  passwordHash?: string; // 密码哈希
  avatar?: string; // 头像URL
  status?: string; // active|inactive|suspended
  subscription?: string; // free|premium|vip
  planRole?: string; // 用户计划角色
  isVerified?: boolean; // 是否已验证
  role?: string; // 用户角色
  createdAt?: number; // 创建时间戳
  updatedAt?: number; // 更新时间戳
  lastLoginAt?: string; // 最后登录时间
  code?: string; // 验证码
}
// 人设
export interface IPersona {
  userId: string; // 用户ID
  personaId: string; // 人设唯一标识
  name: string; // 人设名称
  gender: string; // 性别
  mbti: string; // MBTI类型
  personality: string; // 性格特点
  introduction: string; // 详细介绍
  topics: string; // 擅长话题
  isDefault: boolean; // 是否默认人设
  createdAt: string;
  updatedAt: string;
}

// photo
export interface IPhoto {
  id: string; // 照片唯一标识
  userId: string; // 用户ID
  personaId: string; // 人设ID
  fileName: string; // 照片名称
  s3Key: string; // S3存储路径
  summary: string; // 照片摘要
  createdAt: string;
  updatedAt: string;
  isUse: boolean;
}

// 信息源
export interface ISource {
  id: string; // 信息源唯一标识
  userId: string; // 用户ID
  personaId: string; // 人设ID
  type: 'rss' | 'url' | 'account'; // 信息源类型
  name: string; // 信息源名称
  url: string; // 信息源URL
  isBuiltIn: boolean; // 是否内置信息源
  isDomestic: boolean; // 国内/国外
  isConnection: boolean; // 是否已连接
  contentType: string[]; // 内容类型
  createdAt: string;
  updatedAt: string;
}

// file-pdf
export interface IFile {
  id: string; // 文件唯一标识
  userId: string; // 用户ID
  personaId: string; // 人设ID
  fileName: string; // 文件名称
  fileSize: number; // 文件大小
  fileType: string; // 文件类型
  imgStatus: StatusEnum; // 图片处理状态
  s3Key: string; // S3存储路径
  embeddingStatus: StatusEnum; // 向量处理状态
  totalPage: number; // 总页数
  createdAt: string;
  updatedAt: string;
}

export interface IFilePage {
  id: string; // 页面唯一标识
  fileId: string; // 文件ID
  page: number; // 页码
  content: string; // 页面内容
  s3Key?: string; // 页面图片S3路径
  userId: string; // 用户ID
  personaId: string; // 人设ID
  embedding?: number[]; // 向量嵌入
  summary?: string; // 页面摘要
}

export interface IConfig {
  id: string; // 配置唯一标识
  userId: string; // 用户ID
  personaId: string; // 人设ID
  writingStyle: 'warmAndCute' | 'professionalAndStrict' | 'livelyAndFun' | 'literaryAndFresh';
  contentLength: 'short' | 'medium' | 'long';
  writingFields: string[];
  createdAt: string;
  updatedAt: string;
}

export interface IGenerateList {
  id?: string;
  userId?: string;
  personaId?: string;
  finalContent: string;
  title?: string;
  tags?: string[];
  style?: string;
  reasoning?: string;
  photos?: string[];
  createdAt?: string;
  updatedAt?: string;
  taskId?: string;
  status?: string;
}
