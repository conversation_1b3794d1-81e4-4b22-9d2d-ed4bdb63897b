import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';

import { completeMultipartUpload } from '@shared/utils/s3';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { validateRequest } from '@shared/middleware/validation';
import { globalErrorHandler, handleExternalServiceError } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';

// 定义完成上传验证规则
const completeUploadValidationSchema = {
  fileKey: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 500,
  },
  uploadId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
  },
};

const completeUploadHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);
  let userId = '';

  try {
    // 身份验证
    userId = checkAuth(event);
    // 输入验证
    const validator = validateRequest(completeUploadValidationSchema);
    const validatedData = validator(event);
    const { fileKey, uploadId } = validatedData;
    // 手动验证parts参数
    const body = JSON.parse(event.body || '{}');
    const { parts } = body;
    if (!Array.isArray(parts) || parts.length === 0) {
      throw new Error('parts参数必须是非空数组');
    }
    log.businessEvent('UPLOAD_COMPLETE_REQUESTED', userId, {
      fileKey,
      uploadId,
      partsCount: parts.length,
      requestId,
    });

    // 完成多部分上传
    const result = await completeMultipartUpload(
      process.env.XHS_SOURCE_BUCKET_NAME!,
      fileKey,
      uploadId,
      parts,
    );

    log.businessEvent('UPLOAD_COMPLETE_SUCCESS', userId, {
      fileKey,
      uploadId,
      location: result.Location,
      requestId,
    });

    return ResponseWrapper.success(
      {
        result,
        fileKey,
        uploadId,
        location: result.Location,
        success: true,
      },
      requestId,
    );
  } catch (error) {
    log.error('Upload complete failed', error as Error, {
      userId,
      requestId,
    });

    return handleExternalServiceError(error, 'S3');
  }
};

export const handler: Handler = globalErrorHandler(completeUploadHandler);
