import { DeleteCommand, GetCommand, PutCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';

import { getDBClient } from './init';

const dbClient = getDBClient();
const TABLE_NAME = 'XHS_TWITTER_SESSIONS';

// Twitter session interface based on user requirements
export interface ITwitterSession {
  userId: string; // partition key
  accessToken: string; // encrypted access token
  refreshToken: string; // encrypted refresh token
  expiresAt: string; // ISO string format
  twitterUserId: string;
  twitterUsername: string;
  createdAt: string; // ISO string format
  updatedAt: string; // ISO string format
}

/**
 * Create or update Twitter session
 * @param data Twitter session data
 */
export async function saveTwitterSession(data: ITwitterSession): Promise<void> {
  try {
    const command = new PutCommand({
      TableName: TABLE_NAME,
      Item: {
        ...data,
        updatedAt: new Date().toISOString(),
      },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('Save Twitter session failed', error);
    throw error;
  }
}

/**
 * Get Twitter session by user ID
 * @param userId User ID
 * @returns Twitter session or null
 */
export async function getTwitterSession(userId: string): Promise<ITwitterSession | null> {
  try {
    const command = new GetCommand({
      TableName: TABLE_NAME,
      Key: { userId },
    });
    const result = await dbClient.send(command);
    return (result.Item as ITwitterSession) || null;
  } catch (error) {
    console.error('Get Twitter session failed', error);
    throw error;
  }
}

/**
 * Update Twitter session
 * @param userId User ID
 * @param updates Update data
 */
export async function updateTwitterSession(
  userId: string,
  updates: Partial<Omit<ITwitterSession, 'userId'>>,
): Promise<void> {
  try {
    const updateExpressions: string[] = [];
    const expressionAttributeNames: Record<string, string> = {};
    const expressionAttributeValues: Record<string, any> = {};

    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        updateExpressions.push(`#${key} = :${key}`);
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributeValues[`:${key}`] = value;
      }
    });

    if (updateExpressions.length === 0) {
      return;
    }

    // Always update the updatedAt field
    updateExpressions.push('#updatedAt = :updatedAt');
    expressionAttributeNames['#updatedAt'] = 'updatedAt';
    expressionAttributeValues[':updatedAt'] = new Date().toISOString();

    const command = new UpdateCommand({
      TableName: TABLE_NAME,
      Key: { userId },
      UpdateExpression: `SET ${updateExpressions.join(', ')}`,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
    });

    await dbClient.send(command);
  } catch (error) {
    console.error('Update Twitter session failed', error);
    throw error;
  }
}

/**
 * Delete Twitter session
 * @param userId User ID
 */
export async function deleteTwitterSession(userId: string): Promise<void> {
  try {
    const command = new DeleteCommand({
      TableName: TABLE_NAME,
      Key: { userId },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('Delete Twitter session failed', error);
    throw error;
  }
}

/**
 * Check if Twitter session exists and is valid
 * @param userId User ID
 * @returns Session status
 */
export async function checkTwitterSessionStatus(userId: string): Promise<{
  exists: boolean;
  expired: boolean;
  session?: ITwitterSession;
}> {
  try {
    const session = await getTwitterSession(userId);

    if (!session) {
      return { exists: false, expired: false };
    }

    const isExpired = new Date() > new Date(session.expiresAt);

    return {
      exists: true,
      expired: isExpired,
      session: isExpired ? undefined : session,
    };
  } catch (error) {
    console.error('Check Twitter session status failed', error);
    throw error;
  }
}
