import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { getGenerationTaskContentStatistics } from '@infrastructure/database/db/generationTasks';

// 定义统计验证规则
const statisticsValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
};

/**
 * 获取生成内容统计接口
 * 从XHS_GENERATION_TASKS表的finalContent中统计模型生成的内容
 * 返回总条数、发布条数、保存条数等统计信息
 */
const statisticsHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('GENERATE_LIST_STATISTICS_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(statisticsValidationSchema);
    const { personaId } = validator(event);

    log.businessEvent('GENERATE_LIST_STATISTICS_PROCESSING', userId, {
      requestId,
      userId,
      personaId,
    });

    // 获取统计信息
    const statistics = await getGenerationTaskContentStatistics(userId, personaId);

    log.businessEvent('GENERATE_LIST_STATISTICS_SUCCESS', userId, {
      requestId,
      userId,
      personaId,
      totalGenerated: statistics.totalGenerated,
      publishedCount: statistics.publishedCount,
      favoritesCount: statistics.favoritesCount,
    });

    return ResponseWrapper.success(
      {
        message: '获取统计信息成功',
        userId,
        personaId,
        statistics: {
          totalGenerated: statistics.totalGenerated, // 总共生成的内容条数（模型生成的条数）
          published: statistics.publishedCount, // 发布条数
          favorites: statistics.favoritesCount, // 保存条数（收藏）
          draft: statistics.initCount, // 草稿条数（初始状态）
          deleted: statistics.deletedCount, // 已删除条数
        },
        metadata: {
          generatedAt: new Date().toISOString(),
          dataSource: 'XHS_GENERATION_TASKS表的finalContent字段',
          description: {
            totalGenerated: '模型生成的总内容条数（来自已完成任务的finalContent）',
            published: '已发布的内容条数',
            favorites: '已保存/收藏的内容条数',
            draft: '草稿状态的内容条数',
            deleted: '已删除的内容条数',
          },
        },
      },
      requestId,
    );
  } catch (error) {
    log.error('Generate list statistics failed', error as Error, {
      userId,
      requestId,
      errorName: (error as Error).name,
      errorMessage: (error as Error).message,
    });

    return ResponseWrapper.error(
      ErrorCode.INTERNAL_ERROR,
      `获取统计信息失败: ${error instanceof Error ? error.message : '未知错误'}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(statisticsHandler);
