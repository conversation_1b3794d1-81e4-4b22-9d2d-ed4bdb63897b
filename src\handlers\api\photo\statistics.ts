import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { getPhotoStatistics } from '@lib/photo';

// 定义验证规则
const statisticsValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
  },
};

/**
 * 获取照片统计信息接口
 * 返回照片总数、使用状态统计、分类统计等信息
 */
const statisticsHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('PHOTO_STATISTICS_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(statisticsValidationSchema);
    const { personaId } = validator(event);

    log.businessEvent('PHOTO_STATISTICS_PROCESSING', userId, {
      requestId,
      personaId,
    });

    // 获取照片统计信息
    const statistics = await getPhotoStatistics(userId, personaId);

    // 计算使用率
    const usageRate = statistics.total > 0 
      ? Math.round((statistics.used / statistics.total) * 100) 
      : 0;

    // 获取最常见的分类（前5个）
    const topTypes = statistics.typeStatistics.slice(0, 5);

    log.businessEvent('PHOTO_STATISTICS_COMPLETED', userId, {
      requestId,
      personaId,
      total: statistics.total,
      used: statistics.used,
      unused: statistics.unused,
      typeCount: statistics.typeStatistics.length,
    });

    return ResponseWrapper.success(
      {
        summary: {
          total: statistics.total,
          used: statistics.used,
          unused: statistics.unused,
          usageRate: `${usageRate}%`,
        },
        usage: {
          used: {
            count: statistics.used,
            percentage: `${usageRate}%`,
          },
          unused: {
            count: statistics.unused,
            percentage: `${100 - usageRate}%`,
          },
        },
        types: {
          total: statistics.typeStatistics.length,
          top5: topTypes,
          all: statistics.typeStatistics,
        },
        filters: {
          userId,
          personaId,
        },
        metadata: {
          generatedAt: new Date().toISOString(),
          dataSource: 'ai_analysis',
        },
      },
      requestId,
    );
  } catch (error) {
    log.error('Photo statistics failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.PHOTO_STATISTICS_FAILED,
      `获取照片统计信息失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(statisticsHandler);
