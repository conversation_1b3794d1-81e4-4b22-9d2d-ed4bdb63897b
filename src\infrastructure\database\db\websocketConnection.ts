import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  PutCommand,
  DeleteCommand,
  GetCommand,
  ScanCommand,
} from '@aws-sdk/lib-dynamodb';

// 连接信息接口
export interface ConnectionInfo {
  connectionId: string;
  userId?: string;
  connectedAt: string;
  lastPingAt?: string;
  metadata?: {
    userAgent?: string;
    ipAddress?: string;
    [key: string]: any;
  };
}

// DynamoDB 客户端初始化
const dynamoClient = new DynamoDBClient({ region: 'ap-southeast-1' });
const docClient = DynamoDBDocumentClient.from(dynamoClient);

// 连接表名
const CONNECTIONS_TABLE = 'XHS_WEBSOCKET';

// 辅助函数：创建带TTL的连接项
const createConnectionItem = (connectionInfo: ConnectionInfo) => ({
  ...connectionInfo,
  ttl: Math.floor(Date.now() / 1000) + 24 * 60 * 60, // 24小时TTL
});

// 辅助函数：安全执行数据库操作
const safeDbOperation = async <T>(
  operation: () => Promise<T>,
  errorMessage: string,
  defaultValue?: T,
): Promise<T> => {
  try {
    return await operation();
  } catch (error) {
    console.error(errorMessage, error);
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw error;
  }
};

/**
 * 添加新连接
 */
export const addConnection = async (connectionInfo: ConnectionInfo): Promise<void> => {
  const command = new PutCommand({
    TableName: CONNECTIONS_TABLE,
    Item: createConnectionItem(connectionInfo),
  });

  await safeDbOperation(() => docClient.send(command), 'Failed to add connection');

  console.log(`Connection added: ${connectionInfo.connectionId}`);
};

/**
 * 移除连接
 */
export const removeConnection = async (connectionId: string): Promise<void> => {
  const command = new DeleteCommand({
    TableName: CONNECTIONS_TABLE,
    Key: { connectionId },
  });

  await safeDbOperation(() => docClient.send(command), 'Failed to remove connection');

  console.log(`Connection removed: ${connectionId}`);
};

/**
 * 获取连接信息
 */
export const getConnection = async (connectionId: string): Promise<ConnectionInfo | null> => {
  const command = new GetCommand({
    TableName: CONNECTIONS_TABLE,
    Key: { connectionId },
  });

  try {
    const result = await docClient.send(command);
    return (result.Item as ConnectionInfo) || null;
  } catch (error) {
    console.error('Failed to get connection:', error);
    return null;
  }
};

/**
 * 更新连接信息
 */
export const updateConnection = async (connectionInfo: ConnectionInfo): Promise<void> => {
  const command = new PutCommand({
    TableName: CONNECTIONS_TABLE,
    Item: createConnectionItem(connectionInfo),
  });

  await safeDbOperation(() => docClient.send(command), 'Failed to update connection');
};

/**
 * 获取用户的所有连接
 */
export const getUserConnections = async (userId: string): Promise<ConnectionInfo[]> => {
  const command = new ScanCommand({
    TableName: CONNECTIONS_TABLE,
    FilterExpression: 'userId = :userId',
    ExpressionAttributeValues: {
      ':userId': userId,
    },
  });

  try {
    const result = await docClient.send(command);
    return (result.Items as ConnectionInfo[]) || [];
  } catch (error) {
    console.error('Failed to get user connections:', error);
    return [];
  }
};

/**
 * 获取所有活跃连接
 */
export const getAllConnections = async (): Promise<ConnectionInfo[]> => {
  const command = new ScanCommand({
    TableName: CONNECTIONS_TABLE,
  });

  try {
    const result = await docClient.send(command);
    return (result.Items as ConnectionInfo[]) || [];
  } catch (error) {
    console.error('Failed to get all connections:', error);
    return [];
  }
};

/**
 * 批量删除连接
 */
export const removeConnections = async (connectionIds: string[]): Promise<void> => {
  const deletePromises = connectionIds.map(removeConnection);
  await Promise.allSettled(deletePromises);
};
