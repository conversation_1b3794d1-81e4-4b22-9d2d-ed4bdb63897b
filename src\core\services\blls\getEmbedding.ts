import { getSettingPdfSearch } from '@lib/setting';
import { getPdfPage } from '@lib/pdfPage';

import { aiChatString } from '@shared/utils/llm';
import { getAzureTextEmbeddings } from '@shared/utils/embedding';
import { queryVectorStore } from '@shared/utils/vectorStore';

export interface Summary {
  title_en: string;
  title_zh: string;
  summary_zh: string;
}
export interface SearchResult {
  id: string;
  pdfId: string;
  page: number;
  content: string;
  summary?: Summary;
  s3Key?: string;
}

// 分析搜索意图
export async function analyzeSearchIntent(query: string) {
  try {
    const { intentPrompt } = await getSettingPdfSearch();
    if (!intentPrompt) {
      return query;
    }
    const messages = [
      { role: 'system', content: intentPrompt },
      { role: 'user', content: query },
    ];
    const response = await aiChatString(messages);
    return response;
  } catch (error) {
    return query;
  }
}
// 搜索和生成摘要
export async function searchAndSummarize(
  query: string,
  offset: number,
  userId: string,
): Promise<SearchResult[]> {
  try {
    const optimizedQuery = await analyzeSearchIntent(query);
    const azureEmbedding = await getAzureTextEmbeddings([optimizedQuery]);
    const queryEmbedding = azureEmbedding[0].embedding;
    console.info('queryEmbedding:', JSON.stringify(queryEmbedding));

    // 搜索向量存储
    const results = await queryVectorStore({
      queryEmbedding,
      offset: offset || 0,
      userId,
    });
    console.info('results===>:', results);
    const processedResults = await Promise.all(
      results.map(async (hit) => {
        const pdf = await getPdfPage(hit.id, userId);
        if (!pdf.id) return {} as SearchResult;
        return {
          id: pdf.id,
          pdfId: pdf.pdfId,
          page: Number(pdf.page),
          content: pdf.content,
          s3Key: pdf.s3Key,
        } as SearchResult;
      }),
    );
    const result = processedResults.filter((r) => r.content);
    return result;
  } catch (error) {
    console.error('Error searching and summarizing:', error);
    throw error;
  }
}
