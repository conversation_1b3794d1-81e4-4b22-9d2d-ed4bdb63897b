import { APIGatewayEvent } from 'aws-lambda';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

export function checkAuth(event: APIGatewayEvent): string {
  const user = event.requestContext.authorizer?.lambda?.user;

  if (user?.userId) {
    return user.userId as string;
  }

  throw new Error('Unauthorized');
}
export function getLanguage(event: APIGatewayEvent): string {
  const lang = event.headers?.['x-language'] || 'en';
  return lang;
}

export async function hashPassword(plainPassword: string) {
  try {
    const saltRounds = 10;
    // 生成盐
    const salt = await bcrypt.genSalt(saltRounds);
    // 使用盐和密码生成哈希
    const hashedPassword = await bcrypt.hash(plainPassword, salt);
    return hashedPassword;
  } catch (error) {
    console.error('Error hashing password:', error);
    throw error;
  }
}

export function encodeVerifyCode(data: { userId: string; action: string }) {
  return Buffer.from(JSON.stringify(data)).toString('base64');
}

export function decodeVerifyCode(encodedData: string) {
  try {
    const decodedData = Buffer.from(encodedData, 'base64').toString('utf-8');
    const result = JSON.parse(decodedData) as { userId: string; action: string };
    if (
      !result ||
      !result.userId ||
      !result.action ||
      !['register', 'resetPassword'].includes(result.action)
    ) {
      return null;
    }
    return result;
  } catch (e) {
    return null;
  }
}
export async function comparePassword(plainPassword: string, hashedPassword: string) {
  const isMatch = await bcrypt.compare(plainPassword, hashedPassword);
  return isMatch;
}
/**
 * 生成token
 * @param userId 用户id
 * @returns token
 */
export async function generateAccessToken(userId: string) {
  if (!userId) return null;
  const secretKey = process.env.ACCESS_TOKEN_SECRET_KEY;
  const token = jwt.sign({ userId }, secretKey!, {
    expiresIn: '7d',
  });
  return token;
}

export function getLang(event: APIGatewayEvent): string {
  const lang = event.headers?.swlang || event.headers?.swLang || 'en';
  return lang;
}
