{"compilerOptions": {"baseUrl": ".", "paths": {"@src/*": ["src/*"], "@handlers/*": ["src/handlers/*"], "@core/*": ["src/core/*"], "@infrastructure/*": ["src/infrastructure/*"], "@shared/*": ["src/shared/*"], "@lib/*": ["src/infrastructure/database/db/*"], "@utils/*": ["src/shared/utils/*"], "@constants/*": ["src/shared/constants/*"], "@services/*": ["src/core/services/*"], "@types/*": ["src/core/types/*"], "@shared/types/*": ["src/shared/types/*"], "@shared/utils/*": ["src/shared/utils/*"]}, "lib": ["esnext.asynciterable", "es6", "es2017", "dom", "dom.iterable"], "moduleResolution": "node", "resolveJsonModule": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "noUnusedLocals": true, "noUnusedParameters": true, "removeComments": true, "sourceMap": true, "target": "esnext", "outDir": ".esbuild", "module": "CommonJS", "esModuleInterop": true, "strict": true, "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", ".serverless/**/*", ".webpack/**/*", "_warmup/**/*", ".vscode/**/*", ".esbuild/**/*"], "ts-node": {"require": ["tsconfig-paths/register"]}}