import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { createConfig, getConfigByUserAndPersona, updateConfigByUserAndPersona } from '@lib/config';
import { v4 as uuidv4 } from 'uuid';
// 定义配置验证规则 (获取或创建/更新)
const configValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
  writingStyle: {
    required: false,
    type: 'string' as const,
    enum: ['warmAndCute', 'professionalAndStrict', 'livelyAndFun', 'literaryAndFresh'],
  },
  contentLength: {
    required: false,
    type: 'string' as const,
    enum: ['short', 'medium', 'long'],
  },
};

const configHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);
  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('CONFIG_GET_OR_CREATE_STARTED', userId, {
      requestId,
    });

    const validator = validateRequest(configValidationSchema);
    const { personaId, writingStyle, contentLength } = validator(event);

    // 解析请求体中的其他字段
    const { writingFields } = JSON.parse(event.body || '{}');

    // 先尝试获取现有配置
    const existingConfig = await getConfigByUserAndPersona(userId, personaId);

    if (existingConfig) {
      // 如果配置存在，检查是否需要更新
      const updateData: any = {};
      if (writingStyle !== undefined) updateData.writingStyle = writingStyle;
      if (contentLength !== undefined) updateData.contentLength = contentLength;
      if (writingFields !== undefined) updateData.writingFields = writingFields;

      if (Object.keys(updateData).length > 0) {
        // 有更新数据，执行更新
        await updateConfigByUserAndPersona(userId, personaId, updateData);

        // 获取更新后的配置
        const updatedConfig = await getConfigByUserAndPersona(userId, personaId);

        log.businessEvent('CONFIG_UPDATE_SUCCESS', userId, {
          requestId,
          personaId,
          updatedFields: Object.keys(updateData),
        });

        return ResponseWrapper.success(
          {
            message: '配置更新成功',
            config: updatedConfig,
            action: 'updated',
            updatedFields: Object.keys(updateData),
          },
          requestId,
        );
      }
      // 没有更新数据，直接返回现有配置
      log.businessEvent('CONFIG_GET_SUCCESS', userId, {
        requestId,
        personaId,
      });

      return ResponseWrapper.success(
        {
          message: '获取配置成功',
          config: existingConfig,
          action: 'retrieved',
        },
        requestId,
      );
    }
    // 配置不存在，创建新配置
    const configId = uuidv4();

    const insertData = {
      id: configId,
      userId,
      personaId,
      writingStyle: writingStyle || 'warmAndCute', // 默认值
      contentLength: contentLength || 'short', // 默认值
      writingFields: writingFields || ['lifestyle'], // 默认值
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await createConfig(insertData);

    log.businessEvent('CONFIG_CREATE_SUCCESS', userId, {
      requestId,
      configId,
      personaId,
    });

    return ResponseWrapper.success(
      {
        message: '配置创建成功',
        config: insertData,
        action: 'created',
      },
      requestId,
    );
  } catch (error) {
    log.error('Config operation failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.INTERNAL_ERROR,
      `配置操作失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(configHandler);
