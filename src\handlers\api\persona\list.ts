import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { getPersonasByUserId } from '@lib/personas';
import { ErrorCode } from '@src/shared/types/response';

const listHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('PERSONA_LIST_REQUESTED', userId, {
      requestId,
    });

    // 获取用户的身份列表
    const personas = await getPersonasByUserId(userId);

    log.businessEvent('PERSONA_LIST_RETRIEVED', userId, {
      requestId,
      count: personas.length,
    });

    return ResponseWrapper.success(
      {
        personas,
        count: personas.length,
      },
      requestId,
    );
  } catch (error) {
    log.error('Persona list retrieval failed', error as Error, {
      userId,
      requestId,
    });
    return ResponseWrapper.error(
      ErrorCode.PERSONA_LIST_FAILED,
      `获取身份列表失败${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(listHandler);
