import { MilvusClient } from '@zilliz/milvus2-sdk-node';
import { getSettingPdfIndex } from '@lib/setting';
import logger from '@shared/utils/logger';
import { VECTOR_LIMIT } from './const';

let client: MilvusClient | null = null;
interface VectorData {
  id: string;
  pdf_id: string;
  page: number;
  [key: string]: any;
}

interface SearchResult {
  id: string;
}

/**
 * 获取 Milvus 客户端实例
 */
function getClient(): MilvusClient {
  if (client) {
    return client;
  }
  const address = process.env.MILVUS_URI;
  const token = process.env.MILVUS_KEY;
  if (!address || !token) {
    throw new Error('Missing Milvus configuration');
  }

  logger.info(`getClient address: ${address} token: ${token}`);

  client = new MilvusClient({
    address,
    token,
  });

  logger.info(`getClient client: ${JSON.stringify(client)}`);
  return client;
}

/**
 * 获取集合名称
 */
async function getCollectionName(): Promise<string> {
  const setting = await getSettingPdfIndex();
  return setting.pdfVectorCollection || 'pdf';
}

/**
 * 保存向量数据到 Milvus
 */
export async function saveVectorStore(docs: any[]) {
  try {
    const milvusClient = getClient();
    const collectionName = await getCollectionName();
    const res = await milvusClient.insert({
      collection_name: collectionName,
      data: docs,
    });
    console.info(`saveVectorStore res: ${JSON.stringify(res)}`);
    if (res.succ_index.length === 0) {
      throw new Error('Failed to save vector data');
    }
  } catch (error) {
    console.error('Error saving to vector store:', error);
    throw error; // 向上抛出错误以便调用方处理
  }
}

/**
 * 查询向量数据
 */
export async function queryVectorStore({
  queryEmbedding,
  limit = VECTOR_LIMIT,
  offset = 0,
  userId,
  personaId,
}: {
  queryEmbedding?: number[];
  limit?: number;
  offset?: number;
  userId?: string;
  personaId?: string;
}): Promise<SearchResult[]> {
  if (!queryEmbedding || queryEmbedding.length === 0) {
    throw new Error('queryEmbedding is required for vector search');
  }
  if (!userId) {
    throw new Error('userId is required');
  }

  const escape = (val: string) => val.replace(/"/g, '\\"');

  let filter = `userId == "${escape(userId)}"`;
  if (personaId) {
    filter += ` && personaId == "${escape(personaId)}"`;
  }

  try {
    const milvusClient = getClient();
    const response = await milvusClient.search({
      collection_name: await getCollectionName(),
      vector: queryEmbedding,
      anns_field: 'embedding',
      limit,
      offset,
      params: { metric_type: 'COSINE' },
      output_fields: ['id', 'userId'],
      filter,
    });

    return response.results.map((result) => ({
      id: result.id ?? result.fields?.id, // 双保险
    }));
  } catch (error) {
    console.error('Error querying vector store:', error);
    throw error;
  }
}

/**
 * 获取指定ID的向量数据
 */
export async function getVectorData(
  vectorId: string,
  fields?: string[],
): Promise<VectorData | null> {
  try {
    const milvusClient = getClient();
    const response = await milvusClient.query({
      collection_name: await getCollectionName(),
      filter: `id in [${vectorId}]`,
      output_fields: fields || ['*'],
    });

    if (!response.data || response.data.length === 0) {
      return null;
    }

    return response.data[0] as VectorData;
  } catch (error) {
    console.error('Error getting vector data:', error);
    throw error;
  }
}

/**
 * 删除向量数据,批量删除
 */
export async function deleteVectorData(vectorIds: string[]) {
  const milvusClient = getClient();
  await milvusClient.delete({
    collection_name: await getCollectionName(),
    filter: `id in ${JSON.stringify(vectorIds)}`,
  });
  await milvusClient.flush({
    collection_names: [await getCollectionName()],
  });
}

/**
 * 重置客户端连接
 */
export function resetClient(): void {
  if (client) {
    client = null;
  }
}
