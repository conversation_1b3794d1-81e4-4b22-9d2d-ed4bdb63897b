/* eslint-disable no-await-in-loop */
import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';

import { checkAuth } from '@shared/utils/auth';
import { deleteFileById, getFileById } from '@lib/file';
import { listPagesByFileId, deleteFilePageById } from '@lib/filePage';
import { deleteVectorData } from '@shared/utils/vectorStore';

import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { NotFoundError, ApiError } from '@shared/utils/errors';
import { ErrorCode } from '@shared/types/response';
import { validateRequest } from '@shared/middleware/validation';
import {
  globalErrorHandler,
  handleDatabaseError,
  handleExternalServiceError,
} from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';

// 定义删除验证规则
const deleteValidationSchema = {
  fileId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 300, // 支持长用户ID的文件ID格式
  },
};
// 删除操作的结果接口
interface DeleteOperationResult {
  success: boolean;
  pageId: string;
  error?: Error;
}

// 删除统计接口
interface DeleteStats {
  totalPages: number;
  successfulDeletes: number;
  failedDeletes: number;
  failedPageIds: string[];
}

// 页面数据接口
interface PageData {
  id: string;
  fileId: string;
  userId: string;
  pageNumber?: number;
  [key: string]: any;
}

/**
 * 带重试机制的单页面删除
 */
async function deletePageWithRetry(
  page: PageData,
  maxRetries: number,
  fileId: string,
  requestId: string,
): Promise<DeleteOperationResult> {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries + 1; attempt += 1) {
    try {
      // 设置超时保护
      const deleteWithTimeout = Promise.race([
        Promise.all([deleteVectorData([page.id]), deleteFilePageById(page.id)]),
        new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Delete operation timeout')), 30000);
        }),
      ]);

      // eslint-disable-next-line no-await-in-loop
      await deleteWithTimeout;

      log.debug('Page deleted successfully', {
        fileId,
        pageId: page.id,
        attempt,
        requestId,
      });

      return {
        success: true,
        pageId: page.id,
      };
    } catch (error) {
      lastError = error as Error;

      log.warn(`Page deletion attempt ${attempt} failed`, {
        fileId,
        pageId: page.id,
        error: lastError.message,
        requestId,
      });

      // 如果不是最后一次尝试，等待后重试
      if (attempt <= maxRetries) {
        const delay = Math.min(1000 * 2 ** (attempt - 1), 5000); // 指数退避，最大5秒
        // eslint-disable-next-line no-await-in-loop
        await new Promise<void>((resolve) => {
          setTimeout(resolve, delay);
        });
      }
    }
  }

  return {
    success: false,
    pageId: page.id,
    error: lastError || new Error('Unknown deletion error'),
  };
}

/**
 * 批量删除页面数据的改进实现
 * 使用分批处理和重试机制，提高删除成功率
 */
async function performBatchPageDeletion(
  pages: PageData[],
  fileId: string,
  requestId: string,
): Promise<DeleteOperationResult[]> {
  const BATCH_SIZE = 5; // 减小批次大小以提高稳定性
  const MAX_RETRIES = 2;
  const results: DeleteOperationResult[] = [];

  // 分批处理页面删除
  for (let i = 0; i < pages.length; i += BATCH_SIZE) {
    const batch = pages.slice(i, i + BATCH_SIZE);

    log.info(`Processing deletion batch ${Math.floor(i / BATCH_SIZE) + 1}`, {
      fileId,
      batchSize: batch.length,
      totalBatches: Math.ceil(pages.length / BATCH_SIZE),
      requestId,
    });

    // 并行处理当前批次
    const batchPromises = batch.map(async (page) =>
      deletePageWithRetry(page, MAX_RETRIES, fileId, requestId),
    );

    const batchResults = await Promise.allSettled(batchPromises);

    // 处理批次结果
    batchResults.forEach((result, index) => {
      const page = batch[index];
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        results.push({
          success: false,
          pageId: page.id,
          error: new Error(`Batch processing failed: ${result.reason}`),
        });
      }
    });

    // 批次间短暂延迟，避免过载
    if (i + BATCH_SIZE < pages.length) {
      await new Promise<void>((resolve) => {
        setTimeout(resolve, 200);
      });
    }
  }

  return results;
}

/**
 * 删除PDF文件及其相关数据
 * 采用分阶段删除策略，确保数据一致性：
 * 1. 验证文件存在性和用户权限
 * 2. 获取所有相关页面数据
 * 3. 批量删除页面数据和向量数据（允许部分失败）
 * 4. 删除主文件记录
 * 5. 记录删除统计和清理结果
 */
const deleteHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);
  let userId = '';
  let fileInfo: any = null;
  const deleteStats: DeleteStats = {
    totalPages: 0,
    successfulDeletes: 0,
    failedDeletes: 0,
    failedPageIds: [],
  };

  try {
    // 第一阶段：身份验证和输入验证
    userId = checkAuth(event);
    const validator = validateRequest(deleteValidationSchema);
    const validatedData = validator(event);
    const { fileId } = validatedData;

    // 第二阶段：检查文件存在性和权限
    fileInfo = await getFileById(fileId);
    if (!fileInfo) {
      throw new NotFoundError('文件');
    }

    // 验证用户是否有权限删除此文件
    if (fileInfo.userId !== userId) {
      throw new ApiError(ErrorCode.ACCESS_DENIED, '无权限删除此文件');
    }

    log.businessEvent('FILE_DELETE_STARTED', userId, {
      fileId,
      fileName: fileInfo.fileName,
      requestId,
    });

    // 第三阶段：获取所有相关页面
    const pages = await listPagesByFileId(fileId, userId);
    deleteStats.totalPages = pages.length;

    log.info('Found pages for deletion', {
      fileId,
      pageCount: pages.length,
      requestId,
    });

    // 第四阶段：批量删除页面数据（使用改进的批处理策略）
    if (pages.length > 0) {
      const deleteResults = await performBatchPageDeletion(pages as PageData[], fileId, requestId);
      deleteStats.successfulDeletes = deleteResults.filter((r) => r.success).length;
      deleteStats.failedDeletes = deleteResults.filter((r) => !r.success).length;
      deleteStats.failedPageIds = deleteResults.filter((r) => !r.success).map((r) => r.pageId);
    }

    // 第五阶段：删除主文件记录
    await deleteFileById(fileId);

    // 记录完成状态
    log.businessEvent('FILE_DELETE_COMPLETED', userId, {
      fileId,
      fileName: fileInfo.fileName,
      deleteStats,
      requestId,
    });

    // 构建响应消息
    const responseMessage =
      deleteStats.failedDeletes > 0
        ? `文件删除完成，但有 ${deleteStats.failedDeletes} 个页面删除失败`
        : '文件删除成功';

    return ResponseWrapper.success(
      {
        message: responseMessage,
        fileId,
        fileName: fileInfo.fileName,
        deleteStats,
        warnings:
          deleteStats.failedDeletes > 0
            ? ['部分页面数据删除失败，但不影响文件主体删除']
            : undefined,
      },
      requestId,
    );
  } catch (error) {
    log.error('File deletion failed', error as Error, {
      userId,
      fileId: fileInfo?.id,
      fileName: fileInfo?.fileName,
      deleteStats,
      requestId,
    });

    // 处理已知错误类型
    if (error instanceof NotFoundError || error instanceof ApiError) {
      throw error;
    }

    // 根据错误类型进行分类处理
    const errorMessage = (error as Error).message || '';
    if (errorMessage.includes('DynamoDB') || errorMessage.includes('database')) {
      handleDatabaseError(error, 'deleteFile');
    }
    if (errorMessage.includes('Milvus') || errorMessage.includes('vector')) {
      handleExternalServiceError(error, 'Milvus');
    }
    throw error;
  }
};

export const handler: Handler = globalErrorHandler(deleteHandler);
