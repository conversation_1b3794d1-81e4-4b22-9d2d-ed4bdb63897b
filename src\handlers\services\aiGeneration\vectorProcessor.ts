// 向量搜索队列
import 'dotenv/config';
import { SQSEvent, SQSRecord } from 'aws-lambda';
import { VectorSearchMessage, TaskStatus, ProcessingStage } from '@core/types/generationTask';
import { deleteMessage, sendMessage } from '@infrastructure/messaging/sqs';
import { log } from '@shared/utils/structuredLogger';
import { updateTaskStatus } from '@infrastructure/database/db/generationTasks';
import { getAzureTextEmbeddings } from '@shared/utils/embedding';
import { queryVectorStore } from '@src/shared/utils/vectorStore';
import { getFilePageById } from '@src/infrastructure/database/db/filePage';

/**
 * 处理单个向量搜索任务
 * @param task 任务信息
 */
async function processVectorSearchTask(task: VectorSearchMessage): Promise<void> {
  const { taskId, userId, personaId, keywords, persona, selectedPhotos, connectionId } = task;
  try {
    log.businessEvent('GENERATION_TASK_VECTOR_SEARCH_STARTED', userId, {
      taskId,
      keywordCount: keywords.length,
    });
    const azureEmbedding = await getAzureTextEmbeddings(keywords);
    const queryEmbedding = azureEmbedding[0].embedding;
    const embeddingResult = await queryVectorStore({
      queryEmbedding,
      userId,
      personaId,
    });
    const processedResults = await Promise.all(
      embeddingResult.map(async (hit) => {
        const pdf = await getFilePageById(hit.id);
        return {
          id: pdf.id,
          fileId: pdf.fileId,
          page: Number(pdf.page),
          content: pdf.content,
          s3Key: pdf.s3Key,
        };
      }),
    );
    // 向量搜索后的 topN
    const topNResults = processedResults.filter((r) => r.content);
    // TODO: 调用MCP获取外部数据
    const mcpData: any = [];
    if (mcpData.length === 0 && topNResults.length === 0) {
      await sendMessage(
        JSON.stringify({
          taskId,
          userId,
          personaId,
          keywords,
          topNResults,
          mcpData,
          persona,
          selectedPhotos,
          connectionId,
        }),
        process.env.CONTENT_GENERATION_QUEUE_URL!,
      );
      // 更新任务状态： 进入内容生成阶段
      await updateTaskStatus(
        taskId,
        TaskStatus.PROCESSING,
        ProcessingStage.CONTENT_GENERATION,
        80,
        {
          topNResults,
          mcpData,
        },
      );
    } else {
      await sendMessage(
        JSON.stringify({
          taskId,
          topNResults,
          mcpData,
          userId,
          personaId,
          persona,
          selectedPhotos,
          connectionId,
        }),
        process.env.DATA_MERGE_QUEUE_URL!,
      );
      // 更新任务状态： 进入内容生成阶段
      await updateTaskStatus(
        taskId,
        TaskStatus.PROCESSING,
        ProcessingStage.CONTENT_GENERATION,
        70,
        {
          topNResults,
          mcpData,
        },
      );
    }

    log.businessEvent('GENERATION_TASK_VECTOR_SEARCH_SUCCESS', userId, {
      taskId,
      resultCount: topNResults.length,
    });
  } catch (error) {
    log.error('处理向量搜索任务失败', error as Error, {
      taskId,
      userId,
      personaId,
    });
    await updateTaskStatus(taskId, TaskStatus.FAILED, undefined, undefined, {
      errorMessage: '向量搜索失败',
    });
  }
}

/**
 * SQS 事件处理器 - 处理向量搜索任务
 * @param event SQS事件
 */
export async function handler(event: SQSEvent): Promise<void> {
  console.log(`Processing ${event.Records.length} vector search tasks`);

  // 并行处理所有任务
  const results = await Promise.allSettled(
    event.Records.map(async (record: SQSRecord) => {
      try {
        // 删除 SQS 消息
        try {
          await deleteMessage(process.env.VECTOR_SEARCH_QUEUE_URL!, record.receiptHandle);
        } catch (error) {
          console.error('Error deleting SQS message:', error);
        }

        const task: VectorSearchMessage = JSON.parse(record.body);
        await processVectorSearchTask(task);
        return { success: true, taskId: task.taskId };
      } catch (error) {
        console.error('Failed to process SQS record:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    }),
  );

  // 统计处理结果
  const successful = results.filter(
    (result) => result.status === 'fulfilled' && result.value.success,
  ).length;
  const failed = results.length - successful;
  console.log(`Vector search processing completed: ${successful} successful, ${failed} failed`);

  if (failed > 0) {
    console.error(
      'Some vector search tasks failed:',
      results
        .filter((result) => result.status === 'fulfilled' && !result.value.success)
        .map((result) => (result.status === 'fulfilled' ? result.value.error : 'Unknown error')),
    );
  }
}
