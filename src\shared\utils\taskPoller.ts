import { TaskStatus, TaskStatusResponse, TaskResultResponse } from '@core/types/generationTask';

// Types
export interface PollingOptions {
  pollInterval?: number; // 轮询间隔(毫秒)，默认2000
  maxWaitTime?: number; // 最大等待时间(毫秒)，默认300000 (5分钟)
  onProgress?: (status: TaskStatusResponse) => void; // 进度回调
  onStageChange?: (stage: string) => void; // 阶段变化回调
}

export interface PollingResult {
  success: boolean;
  result?: TaskResultResponse;
  error?: string;
  timeout?: boolean;
}

export interface ApiConfig {
  baseUrl: string;
  authToken: string;
}

export interface PollingState {
  startTime: number;
  lastStage: string;
  currentAttempt: number;
}

// Utility types for functional composition (可在需要时使用)
// type AsyncFunction<T, R> = (arg: T) => Promise<R>;
// type PollingPredicate = (status: TaskStatusResponse) => boolean;
// type ErrorHandler = (error: Error) => void;

// Pure utility functions
const sleep = (ms: number): Promise<void> =>
  new Promise((resolve) => {
    setTimeout(resolve, ms);
  });

const normalizeBaseUrl = (baseUrl: string): string => baseUrl.replace(/\/$/, ''); // 移除末尾斜杠

const createApiConfig = (baseUrl: string, authToken: string): ApiConfig => ({
  baseUrl: normalizeBaseUrl(baseUrl),
  authToken,
});

const createPollingState = (): PollingState => ({
  startTime: Date.now(),
  lastStage: '',
  currentAttempt: 0,
});

// Default options
const defaultPollingOptions: Required<Omit<PollingOptions, 'onProgress' | 'onStageChange'>> = {
  pollInterval: 2000,
  maxWaitTime: 300000,
};

// Status checking predicates
const isTaskCompleted = (status: TaskStatusResponse): boolean =>
  status.status === TaskStatus.COMPLETED;

const isTaskFailed = (status: TaskStatusResponse): boolean => status.status === TaskStatus.FAILED;

const isTaskFinished = (status: TaskStatusResponse): boolean =>
  isTaskCompleted(status) || isTaskFailed(status);

const hasStageChanged = (currentStage: string, lastStage: string): boolean =>
  currentStage !== lastStage;

const isTimeoutReached = (startTime: number, maxWaitTime: number): boolean =>
  Date.now() - startTime >= maxWaitTime;

// HTTP request functions
const createHeaders = (authToken: string) => ({
  Authorization: `Bearer ${authToken}`,
  'Content-Type': 'application/json',
});

const handleHttpResponse = async (response: Response): Promise<any> => {
  if (!response.ok) {
    if (response.status === 404) {
      return null;
    }
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  const data = await response.json();
  return data.data;
};

// API functions (curried for reusability)
const getTaskStatus =
  (config: ApiConfig) =>
  async (taskId: string): Promise<TaskStatusResponse | null> => {
    try {
      const response = await fetch(`${config.baseUrl}/api/ai/generate/${taskId}/status`, {
        method: 'GET',
        headers: createHeaders(config.authToken),
      });
      return await handleHttpResponse(response);
    } catch (error) {
      console.error('获取任务状态失败:', error);
      throw error;
    }
  };

const getTaskResult =
  (config: ApiConfig) =>
  async (taskId: string): Promise<TaskResultResponse> => {
    try {
      const response = await fetch(`${config.baseUrl}/api/ai/generate/${taskId}/result`, {
        method: 'GET',
        headers: createHeaders(config.authToken),
      });
      return await handleHttpResponse(response);
    } catch (error) {
      console.error('获取任务结果失败:', error);
      throw error;
    }
  };

const createGenerationTask =
  (config: ApiConfig) =>
  async (personaId: string, selectedPhotos?: string[], preferences?: any): Promise<string> => {
    try {
      const response = await fetch(`${config.baseUrl}/api/ai/generate`, {
        method: 'POST',
        headers: createHeaders(config.authToken),
        body: JSON.stringify({
          personaId,
          selectedPhotos,
          preferences,
        }),
      });
      const data = await handleHttpResponse(response);
      return data.taskId;
    } catch (error) {
      console.error('创建生成任务失败:', error);
      throw error;
    }
  };

// Result creation functions
const createSuccessResult = (result: TaskResultResponse): PollingResult => ({
  success: true,
  result,
});

const createFailureResult = (error: string): PollingResult => ({
  success: false,
  error,
});

const createTimeoutResult = (): PollingResult => ({
  success: false,
  timeout: true,
  error: '任务执行超时',
});

const createNotFoundResult = (): PollingResult => ({
  success: false,
  error: '任务不存在',
});

// Callback execution functions
const executeProgressCallback =
  (onProgress?: (status: TaskStatusResponse) => void) =>
  (status: TaskStatusResponse): void => {
    if (onProgress) {
      onProgress(status);
    }
  };

const executeStageChangeCallback =
  (state: PollingState, onStageChange?: (stage: string) => void) =>
  (status: TaskStatusResponse): PollingState => {
    if (onStageChange && hasStageChanged(status.stage, state.lastStage)) {
      onStageChange(status.stage);
      return { ...state, lastStage: status.stage };
    }
    return state;
  };

// Core polling logic - functional approach
const pollSingleIteration =
  (config: ApiConfig, options: PollingOptions) =>
  async (
    taskId: string,
    state: PollingState,
  ): Promise<{ result?: PollingResult; newState: PollingState }> => {
    try {
      const status = await getTaskStatus(config)(taskId);

      if (!status) {
        return { result: createNotFoundResult(), newState: state };
      }

      // Execute callbacks
      executeProgressCallback(options.onProgress)(status);
      const newState = executeStageChangeCallback(state, options.onStageChange)(status);

      // Check if task is finished
      if (isTaskFinished(status)) {
        if (isTaskCompleted(status)) {
          const result = await getTaskResult(config)(taskId);
          return { result: createSuccessResult(result), newState };
        }

        if (isTaskFailed(status)) {
          return {
            result: createFailureResult(status.errorMessage || '任务执行失败'),
            newState,
          };
        }
      }

      return { newState: { ...newState, currentAttempt: newState.currentAttempt + 1 } };
    } catch (error) {
      console.error('轮询错误:', error);
      return { newState: { ...state, currentAttempt: state.currentAttempt + 1 } };
    }
  };

// Recursive polling function
const pollUntilComplete =
  (config: ApiConfig) =>
  async (taskId: string, options: PollingOptions = {}): Promise<PollingResult> => {
    const mergedOptions = { ...defaultPollingOptions, ...options };
    const { pollInterval, maxWaitTime } = mergedOptions;

    const pollIteration = pollSingleIteration(config, options);

    const poll = async (state: PollingState): Promise<PollingResult> => {
      // Check timeout
      if (isTimeoutReached(state.startTime, maxWaitTime)) {
        return createTimeoutResult();
      }

      const { result, newState } = await pollIteration(taskId, state);

      if (result) {
        return result;
      }

      // Wait before next iteration
      const waitTime =
        newState.currentAttempt > state.currentAttempt && state.currentAttempt > 0
          ? pollInterval * 2 // Wait longer after error
          : pollInterval;

      await sleep(waitTime);
      return poll(newState);
    };

    return poll(createPollingState());
  };

// High-level API functions
const generateAndWait =
  (config: ApiConfig) =>
  async (
    personaId: string,
    selectedPhotos?: string[],
    preferences?: any,
    options: PollingOptions = {},
  ): Promise<PollingResult> => {
    try {
      const taskId = await createGenerationTask(config)(personaId, selectedPhotos, preferences);
      return await pollUntilComplete(config)(taskId, options);
    } catch (error) {
      return createFailureResult(error instanceof Error ? error.message : '未知错误');
    }
  };

// Factory function for creating a task poller with configuration
export const createTaskPoller = (baseUrl: string, authToken: string) => {
  const config = createApiConfig(baseUrl, authToken);

  return {
    // Core polling function
    pollUntilComplete: pollUntilComplete(config),

    // API functions
    getTaskStatus: getTaskStatus(config),
    getTaskResult: getTaskResult(config),
    createGenerationTask: createGenerationTask(config),

    // High-level functions
    generateAndWait: generateAndWait(config),
  };
};

// Type for the task poller object
export type TaskPoller = ReturnType<typeof createTaskPoller>;

/**
 * 简化的轮询函数，用于快速集成
 * @param taskId 任务ID
 * @param baseUrl API基础URL
 * @param authToken 认证令牌
 * @param options 轮询选项
 */
export const waitForTaskCompletion = async (
  taskId: string,
  baseUrl: string,
  authToken: string,
  options: PollingOptions = {},
): Promise<PollingResult> => {
  const config = createApiConfig(baseUrl, authToken);
  return pollUntilComplete(config)(taskId, options);
};

/**
 * 一键生成并等待结果的便捷函数
 * @param personaId 人设ID
 * @param baseUrl API基础URL
 * @param authToken 认证令牌
 * @param selectedPhotos 选中的照片ID列表
 * @param preferences 生成偏好
 * @param options 轮询选项
 */
export const generateAndWaitForCompletion = async (
  personaId: string,
  baseUrl: string,
  authToken: string,
  selectedPhotos?: string[],
  preferences?: any,
  options: PollingOptions = {},
): Promise<PollingResult> => {
  const config = createApiConfig(baseUrl, authToken);
  return generateAndWait(config)(personaId, selectedPhotos, preferences, options);
};
