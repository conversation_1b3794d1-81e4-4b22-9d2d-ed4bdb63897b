import jwt from 'jsonwebtoken';
import { APIGatewayProxyEvent } from 'aws-lambda';

interface JwtPayload {
  userId?: string;
  sub?: string;
  [key: string]: any;
}

export function getTokenFromHeader(event: APIGatewayProxyEvent): string | null {
  const authHeader = event.headers?.Authorization || event.headers?.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.replace('Bearer ', '');
}

export function verifyToken(token: string): JwtPayload | null {
  try {
    const decoded = jwt.verify(token, process.env.ACCESS_TOKEN_SECRET_KEY!) as JwtPayload;
    return decoded;
  } catch (err) {
    console.warn('[verifyToken] JWT 验证失败：', err);
    return null;
  }
}
