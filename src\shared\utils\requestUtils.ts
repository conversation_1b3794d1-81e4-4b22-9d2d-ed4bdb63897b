import { v4 as uuidv4 } from 'uuid';
import { ResponseWrapper } from './response';
import { ApiError } from './errors';

/**
 * 请求ID提取工具
 */
export function getRequestId(event: any): string {
  return event.requestContext?.requestId || event.headers?.['x-request-id'] || uuidv4();
}

/**
 * 统一错误处理装饰器
 */
export function withErrorHandling(handler: Function) {
  return async (event: any, context: any) => {
    const requestId = getRequestId(event);

    try {
      const result = await handler(event, context);
      return result;
    } catch (error) {
      const err = error as Error;
      console.error('Handler error:', {
        requestId,
        error: err.message,
        stack: err.stack,
        event: JSON.stringify(event, null, 2),
      });

      if (error instanceof ApiError) {
        return ResponseWrapper.error(error.errorCode, error.message, error.details, requestId);
      }

      return ResponseWrapper.fromError(error as Error, requestId);
    }
  };
}
