/* eslint-disable no-await-in-loop */
import { updateFile } from '@lib/file';
import { StatusEnum } from '@constants/enum';
import { getSettingPdfIndex } from '@lib/setting';
import { batchWriteFilePages } from '@lib/filePage';
import { getAzureTextEmbeddingsInBatches } from '@shared/utils/embedding';
import { saveVectorStore } from '@shared/utils/vectorStore';
import { IFilePage } from '@core/types/interface';
import { log } from '@shared/utils/structuredLogger';
import { ocrPdf, getPdfPageChunks, getFilePageDocs } from './file';
import { createImageQueueManager } from './imageQueue';
import { validateProcessingConfig } from './validator';
import { FileProcessingContext, ProcessingConfig, ProcessingResult } from './types';
import { NoValidContentError } from './error/errors';

// 处理阶段枚举
enum ProcessingStage {
  INITIALIZING = 'INITIALIZING',
  OCR_PROCESSING = 'OCR_PROCESSING',
  CONFIG_LOADING = 'CONFIG_LOADING',
  PAGE_PROCESSING = 'PAGE_PROCESSING',
  DATA_STORING = 'DATA_STORING',
  VECTOR_PROCESSING = 'VECTOR_PROCESSING',
  IMAGE_QUEUING = 'IMAGE_QUEUING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

// 处理选项接口
interface ProcessingOptions {
  skipImageGeneration?: boolean;
  maxRetries?: number;
  batchSize?: number;
  enableProgressTracking?: boolean;
}

/**
 * 文件处理器类
 * 负责文件的OCR识别、向量化和图片队列管理
 */
export class FileProcessor {
  private readonly context: FileProcessingContext;

  private readonly imageQueueManager;

  private readonly options: ProcessingOptions;

  private currentStage: ProcessingStage = ProcessingStage.INITIALIZING;

  private readonly maxRetries: number;

  constructor(context: FileProcessingContext, options: ProcessingOptions = {}) {
    this.context = context;
    this.imageQueueManager = createImageQueueManager();
    this.options = {
      skipImageGeneration: false,
      maxRetries: 3,
      batchSize: 10,
      enableProgressTracking: true,
      ...options,
    };
    this.maxRetries = this.options.maxRetries || 3;
  }

  /**
   * 执行完整的文件处理流程
   * @returns 处理结果
   */
  async process(): Promise<ProcessingResult> {
    const startTime = Date.now();

    try {
      this.currentStage = ProcessingStage.INITIALIZING;
      await this.updateStatus(StatusEnum.PARSING);
      this.logProgress('Processing started');

      // 1. OCR识别
      this.currentStage = ProcessingStage.OCR_PROCESSING;
      const pdfDocsPages = await this.executeWithRetry(() => this.performOCR(), 'OCR processing');
      const totalPage = pdfDocsPages.length;

      if (totalPage === 0) {
        throw new Error('No pages found in PDF');
      }

      // 2. 获取和验证配置
      this.currentStage = ProcessingStage.CONFIG_LOADING;
      const config = await this.executeWithRetry(
        () => this.getProcessingConfig(),
        'Config loading',
      );
      this.context.config = config;

      // 3. 处理页面文档
      this.currentStage = ProcessingStage.PAGE_PROCESSING;
      const filePageDocs = await this.executeWithRetry(
        () => this.processPages(pdfDocsPages, totalPage, config),
        'Page processing',
      );

      if (filePageDocs.length === 0) {
        await this.handleNoValidContent(totalPage);
        return {
          totalPages: totalPage,
          validPages: 0,
          vectorCount: 0,
          imageGroups: 0,
        };
      }

      // 4. 存储页面数据
      this.currentStage = ProcessingStage.DATA_STORING;
      const validPages = await this.executeWithRetry(
        () => this.storePageData(filePageDocs),
        'Data storing',
      );

      // 5. 生成和存储向量
      this.currentStage = ProcessingStage.VECTOR_PROCESSING;
      const vectorCount = await this.executeWithRetry(
        () => this.processVectors(filePageDocs),
        'Vector processing',
      );

      // 6. 更新文件状态为成功
      await this.updateStatus(StatusEnum.SUCCESS, totalPage);

      // 7. 队列图片生成（可选）
      let imageGroups = 0;
      if (!this.options.skipImageGeneration) {
        this.currentStage = ProcessingStage.IMAGE_QUEUING;
        imageGroups = await this.executeWithRetry(
          () => this.queueImageGeneration(validPages),
          'Image queuing',
        );
      }

      this.currentStage = ProcessingStage.COMPLETED;
      const result: ProcessingResult = {
        totalPages: totalPage,
        validPages: validPages.length,
        vectorCount,
        imageGroups,
      };

      const processingTime = Date.now() - startTime;
      this.logCompletion(result, processingTime);
      return result;
    } catch (error) {
      this.currentStage = ProcessingStage.FAILED;
      await this.handleError(error as Error);
      throw error;
    }
  }

  /**
   * 带重试机制的执行方法
   * @param operation 要执行的操作
   * @param operationName 操作名称
   * @param maxRetries 最大重试次数
   * @returns 操作结果
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
    maxRetries?: number,
  ): Promise<T> {
    const retries = maxRetries || this.maxRetries;
    let lastError: Error;

    for (let attempt = 1; attempt <= retries; attempt += 1) {
      try {
        this.logProgress(`${operationName} - Attempt ${attempt}/${retries}`);
        // eslint-disable-next-line no-await-in-loop
        const result = await operation();

        if (attempt > 1) {
          this.logProgress(`${operationName} succeeded on attempt ${attempt}`);
        }

        return result;
      } catch (error) {
        lastError = error as Error;

        log.warn(`${operationName} failed on attempt ${attempt}`, {
          fileId: this.context.fileId,
          userId: this.context.userId,
          attempt,
          maxRetries: retries,
          error: lastError.message,
          stage: this.currentStage,
        });

        if (attempt === retries) {
          log.error(`${operationName} failed after ${retries} attempts`, lastError, {
            fileId: this.context.fileId,
            userId: this.context.userId,
            stage: this.currentStage,
          });
          throw lastError;
        }

        // 指数退避策略
        const delay = Math.min(1000 * 2 ** (attempt - 1), 30000);
        await FileProcessor.sleep(delay);
      }
    }

    throw lastError!;
  }

  /**
   * 记录处理进度
   * @param message 进度消息
   */
  private logProgress(message: string): void {
    if (!this.options.enableProgressTracking) return;

    const { fileId, userId, fileName } = this.context;

    log.businessEvent('FILE_PROCESSING_PROGRESS', userId, {
      fileId,
      fileName,
      stage: this.currentStage,
      message,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 睡眠指定毫秒数
   * @param ms 毫秒数
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(resolve, ms);
    });
  }

  /**
   * 执行OCR识别
   */
  private async performOCR() {
    const { fileId, userId, fileName, s3Key } = this.context;

    log.businessEvent('FILE_OCR_STARTED', userId, { fileId, fileName });

    const pdfDocsPages = await ocrPdf(s3Key);

    log.businessEvent('FILE_OCR_COMPLETED', userId, {
      fileId,
      fileName,
      totalPages: pdfDocsPages.length,
    });

    return pdfDocsPages;
  }

  /**
   * 获取处理配置
   */
  private async getProcessingConfig(): Promise<ProcessingConfig> {
    const embeddingSetting = await getSettingPdfIndex();
    const { minTextLength, embeddingBatchSize, embeddingParallelCount } = embeddingSetting;

    const config: ProcessingConfig = {
      minTextLength,
      embeddingBatchSize,
      embeddingParallelCount,
    };

    validateProcessingConfig(config);

    // Store config in context for potential future use
    this.context.config = config;

    return config;
  }

  /**
   * 处理页面文档
   */
  private async processPages(pdfDocsPages: any[], totalPage: number, config: ProcessingConfig) {
    const { fileId, userId, personaId } = this.context;
    const { minTextLength, embeddingBatchSize, embeddingParallelCount } = config;

    // 分页处理
    const pdfPageChunks = await getPdfPageChunks(
      totalPage,
      embeddingParallelCount,
      embeddingBatchSize,
    );

    // 提取页面文档，过滤短文本
    const filePageDocs = await getFilePageDocs(
      pdfDocsPages,
      pdfPageChunks,
      minTextLength,
      fileId,
      personaId,
      userId,
    );

    return filePageDocs;
  }

  /**
   * 存储页面数据
   */
  private async storePageData(filePageDocs: any[]): Promise<IFilePage[]> {
    const { fileId, userId, fileName } = this.context;

    log.businessEvent('FILE_PAGES_SAVING', userId, {
      fileId,
      fileName,
      validPages: filePageDocs.length,
    });

    const validPages = filePageDocs.filter((p) => p?.content !== null) as IFilePage[];
    await batchWriteFilePages(validPages);

    log.businessEvent('FILE_PAGES_SAVED', userId, {
      fileId,
      fileName,
      savedPages: validPages.length,
    });

    return validPages;
  }

  /**
   * 处理向量化
   */
  private async processVectors(filePageDocs: any[]): Promise<number> {
    const { fileId, userId, fileName } = this.context;

    log.businessEvent('FILE_EMBEDDING_STARTED', userId, {
      fileId,
      fileName,
      pageCount: filePageDocs.length,
    });

    // 分批处理向量化，避免内存过载
    const batchSize = this.options.batchSize || 10;
    const allVectors: any[] = [];

    for (let i = 0; i < filePageDocs.length; i += batchSize) {
      const batch = filePageDocs.slice(i, i + batchSize);

      this.logProgress(
        `Processing embedding batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(filePageDocs.length / batchSize)}`,
      );

      try {
        const embedDocs = await getAzureTextEmbeddingsInBatches(batch);
        const vectors = embedDocs.map((doc) => ({
          id: doc.id,
          userId: doc.userId,
          personaId: doc.personaId,
          embedding: doc.embedding! || [],
        }));

        // 分批保存向量
        await saveVectorStore(vectors);
        allVectors.push(...vectors);

        log.businessEvent('FILE_EMBEDDING_BATCH_COMPLETED', userId, {
          fileId,
          fileName,
          batchIndex: Math.floor(i / batchSize) + 1,
          batchSize: vectors.length,
          totalProcessed: allVectors.length,
        });

        // 批次间稍作延迟，避免过载
        if (i + batchSize < filePageDocs.length) {
          await FileProcessor.sleep(500);
        }
      } catch (error) {
        log.error(`Embedding batch ${Math.floor(i / batchSize) + 1} failed`, error as Error, {
          fileId,
          userId,
          batchStart: i,
          batchEnd: Math.min(i + batchSize, filePageDocs.length),
        });
        throw error;
      }
    }

    log.businessEvent('FILE_EMBEDDING_COMPLETED', userId, {
      fileId,
      fileName,
      vectorCount: allVectors.length,
      totalBatches: Math.ceil(filePageDocs.length / batchSize),
    });

    return allVectors.length;
  }

  /**
   * 队列图片生成
   */
  private async queueImageGeneration(filePageDocs: IFilePage[]): Promise<number> {
    const { fileId, userId, s3Key } = this.context;
    return this.imageQueueManager.queueImageGeneration(filePageDocs, fileId, userId, s3Key);
  }

  /**
   * 更新文件状态
   */
  private async updateStatus(status: StatusEnum, totalPage?: number): Promise<void> {
    const { fileId } = this.context;
    const updates: any = { embeddingStatus: status };
    if (totalPage !== undefined) {
      updates.totalPage = totalPage;
    }
    await updateFile(fileId, updates);
  }

  /**
   * 处理无有效内容的情况
   */
  private async handleNoValidContent(totalPage: number): Promise<void> {
    const { fileId, userId, fileName, config } = this.context;
    log.businessEvent('FILE_NO_VALID_CONTENT', userId, {
      fileId,
      fileName,
      totalPages: totalPage,
      minTextLength: config.minTextLength,
    });
    await this.updateStatus(StatusEnum.SUCCESS, totalPage);
    throw new NoValidContentError(fileId, totalPage);
  }

  /**
   * 处理错误
   */
  private async handleError(error: Error): Promise<void> {
    const { fileId, userId, fileName } = this.context;

    // 记录详细的错误信息
    log.error('File processing failed', error, {
      fileId,
      userId,
      fileName,
      stage: this.currentStage,
      errorType: error.constructor.name,
      stack: error.stack,
    });

    // 根据错误类型进行分类处理
    const errorCategory = FileProcessor.categorizeError(error);

    log.businessEvent('FILE_PROCESSING_ERROR', userId, {
      fileId,
      fileName,
      stage: this.currentStage,
      errorCategory,
      errorMessage: error.message,
    });

    try {
      // 更新文件状态为失败
      await updateFile(fileId, {
        embeddingStatus: StatusEnum.FAILED,
        updatedAt: new Date().toISOString(),
      });
    } catch (updateError) {
      log.error('Failed to update file status to FAILED', updateError as Error, {
        fileId,
        userId,
        originalError: error.message,
      });
    }
  }

  /**
   * 错误分类
   */
  private static categorizeError(error: Error): string {
    const errorMessage = error.message.toLowerCase();

    if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
      return 'TIMEOUT_ERROR';
    }
    if (errorMessage.includes('network') || errorMessage.includes('connection')) {
      return 'NETWORK_ERROR';
    }
    if (errorMessage.includes('azure') || errorMessage.includes('embedding')) {
      return 'EMBEDDING_ERROR';
    }
    if (errorMessage.includes('milvus') || errorMessage.includes('vector')) {
      return 'VECTOR_STORE_ERROR';
    }
    if (errorMessage.includes('dynamodb') || errorMessage.includes('database')) {
      return 'DATABASE_ERROR';
    }
    if (errorMessage.includes('s3') || errorMessage.includes('storage')) {
      return 'STORAGE_ERROR';
    }
    if (error instanceof NoValidContentError) {
      return 'NO_VALID_CONTENT';
    }

    return 'UNKNOWN_ERROR';
  }

  /**
   * 记录完成日志
   */
  private logCompletion(result: ProcessingResult, processingTime?: number): void {
    const { fileId, userId, fileName } = this.context;

    log.businessEvent('FILE_INDEX_COMPLETED', userId, {
      fileId,
      fileName,
      processingTimeMs: processingTime,
      processingTimeSec: processingTime ? Math.round(processingTime / 1000) : undefined,
      stage: this.currentStage,
      options: {
        skipImageGeneration: this.options.skipImageGeneration,
        maxRetries: this.maxRetries,
        batchSize: this.options.batchSize,
      },
      ...result,
    });
  }
}

/**
 * 创建文件处理器
 * @param context 处理上下文
 * @param options 处理选项
 * @returns FileProcessor实例
 */
export function createFileProcessor(
  context: FileProcessingContext,
  options?: ProcessingOptions,
): FileProcessor {
  return new FileProcessor(context, options);
}

/**
 * 创建快速处理器（跳过图片生成）
 * @param context 处理上下文
 * @returns FileProcessor实例
 */
export function createFastFileProcessor(context: FileProcessingContext): FileProcessor {
  return new FileProcessor(context, {
    skipImageGeneration: true,
    maxRetries: 2,
    batchSize: 15,
    enableProgressTracking: false,
  });
}

/**
 * 创建高可靠性处理器（更多重试）
 * @param context 处理上下文
 * @returns FileProcessor实例
 */
export function createReliableFileProcessor(context: FileProcessingContext): FileProcessor {
  return new FileProcessor(context, {
    skipImageGeneration: false,
    maxRetries: 5,
    batchSize: 5,
    enableProgressTracking: true,
  });
}
