import 'dotenv/config';

/**
 * 日志级别枚举
 */
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  TRACE = 'trace',
}

/**
 * 日志级别优先级
 */
const LOG_LEVEL_PRIORITY: Record<LogLevel, number> = {
  [LogLevel.ERROR]: 0,
  [LogLevel.WARN]: 1,
  [LogLevel.INFO]: 2,
  [LogLevel.DEBUG]: 3,
  [LogLevel.TRACE]: 4,
};

/**
 * 结构化日志接口
 */
export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  service: string;
  requestId?: string;
  userId?: string;
  operation?: string;
  duration?: number;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
  metadata?: Record<string, any>;
  environment: string;
  version: string;
}

/**
 * 结构化日志器类
 */
export class StructuredLogger {
  private serviceName: string;

  private currentLogLevel: LogLevel;

  constructor(serviceName: string = 'pdf-search-service') {
    this.serviceName = serviceName;
    this.currentLogLevel = StructuredLogger.parseLogLevel(process.env.LOG_LEVEL || 'info');
  }

  private static parseLogLevel(level: string): LogLevel {
    const normalizedLevel = level.toLowerCase() as LogLevel;
    return Object.values(LogLevel).includes(normalizedLevel) ? normalizedLevel : LogLevel.INFO;
  }

  private shouldLog(level: LogLevel): boolean {
    return LOG_LEVEL_PRIORITY[level] <= LOG_LEVEL_PRIORITY[this.currentLogLevel];
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    metadata?: Record<string, any>,
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      service: this.serviceName,
      environment: process.env.NODE_ENV || 'development',
      version: process.env.API_VERSION || '1.0.0',
      ...metadata,
    };
  }

  private static formatLogEntry(entry: LogEntry): string {
    if ((process.env.NODE_ENV || 'development') === 'development') {
      // 开发环境：可读性优先
      const { timestamp, level, message, requestId, userId, operation, duration, error } = entry;
      let logLine = `[${timestamp}] ${level.toUpperCase()}: ${message}`;

      if (requestId) logLine += ` | RequestID: ${requestId}`;
      if (userId) logLine += ` | UserID: ${userId}`;
      if (operation) logLine += ` | Operation: ${operation}`;
      if (duration) logLine += ` | Duration: ${duration}ms`;

      if (error) {
        logLine += `\nError: ${error.name} - ${error.message}`;
        if (error.stack) logLine += `\nStack: ${error.stack}`;
      }

      return logLine;
    }
    // 生产环境：JSON格式，便于日志聚合
    return JSON.stringify(entry);
  }

  private writeLog(entry: LogEntry): void {
    // Double-check log level as a safety measure
    if (!this.shouldLog(entry.level)) return;

    const formattedLog = StructuredLogger.formatLogEntry(entry);

    switch (entry.level) {
      case LogLevel.ERROR:
        console.error(formattedLog);
        break;
      case LogLevel.WARN:
        console.warn(formattedLog);
        break;
      case LogLevel.INFO:
        console.info(formattedLog);
        break;
      case LogLevel.DEBUG:
      case LogLevel.TRACE:
        console.log(formattedLog);
        break;
      default:
        // Fallback for any unexpected log level values
        console.log(formattedLog);
        break;
    }
  }

  /**
   * 错误日志
   */
  error(message: string, error?: Error, metadata?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;

    const entry = this.createLogEntry(LogLevel.ERROR, message, {
      ...metadata,
      error: error
        ? {
            name: error.name,
            message: error.message,
            stack: error.stack,
          }
        : undefined,
    });

    this.writeLog(entry);
  }

  /**
   * 警告日志
   */
  warn(message: string, metadata?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.WARN)) return;

    const entry = this.createLogEntry(LogLevel.WARN, message, metadata);
    this.writeLog(entry);
  }

  /**
   * 信息日志
   */
  info(message: string, metadata?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.INFO)) return;

    const entry = this.createLogEntry(LogLevel.INFO, message, metadata);
    this.writeLog(entry);
  }

  /**
   * 调试日志
   */
  debug(message: string, metadata?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;

    const entry = this.createLogEntry(LogLevel.DEBUG, message, metadata);
    this.writeLog(entry);
  }

  /**
   * 追踪日志
   */
  trace(message: string, metadata?: Record<string, any>): void {
    if (!this.shouldLog(LogLevel.TRACE)) return;

    const entry = this.createLogEntry(LogLevel.TRACE, message, metadata);
    this.writeLog(entry);
  }

  /**
   * 请求开始日志
   */
  requestStart(
    requestId: string,
    method: string,
    path: string,
    metadata?: Record<string, any>,
  ): void {
    this.info('Request started', {
      requestId,
      operation: `${method} ${path}`,
      ...metadata,
    });
  }

  /**
   * 请求完成日志
   */
  requestComplete(
    requestId: string,
    statusCode: number,
    duration: number,
    metadata?: Record<string, any>,
  ): void {
    this.info('Request completed', {
      requestId,
      statusCode,
      duration,
      ...metadata,
    });
  }

  /**
   * 请求失败日志
   */
  requestFailed(
    requestId: string,
    error: Error,
    duration: number,
    metadata?: Record<string, any>,
  ): void {
    this.error('Request failed', error, {
      requestId,
      duration,
      ...metadata,
    });
  }

  /**
   * 数据库操作日志
   */
  databaseOperation(
    operation: string,
    tableName: string,
    duration?: number,
    metadata?: Record<string, any>,
  ): void {
    this.debug('Database operation', {
      operation: `DB:${operation}`,
      tableName,
      duration,
      ...metadata,
    });
  }

  /**
   * 外部服务调用日志
   */
  externalServiceCall(
    serviceName: string,
    operation: string,
    duration?: number,
    metadata?: Record<string, any>,
  ): void {
    this.debug('External service call', {
      operation: `${serviceName}:${operation}`,
      duration,
      ...metadata,
    });
  }

  /**
   * 业务事件日志
   */
  businessEvent(eventName: string, userId?: string, metadata?: Record<string, any>): void {
    this.info('Business event', {
      operation: `EVENT:${eventName}`,
      userId,
      ...metadata,
    });
  }

  /**
   * 性能监控日志
   */
  performance(
    operation: string,
    duration: number,
    success: boolean,
    metadata?: Record<string, any>,
  ): void {
    const level = success ? LogLevel.INFO : LogLevel.WARN;
    const message = `Performance: ${operation}`;

    if (level === LogLevel.INFO) {
      this.info(message, { operation, duration, success, ...metadata });
    } else {
      this.warn(message, { operation, duration, success, ...metadata });
    }
  }

  /**
   * 安全事件日志
   */
  securityEvent(
    eventType: string,
    userId?: string,
    severity: 'low' | 'medium' | 'high' = 'medium',
    metadata?: Record<string, any>,
  ): void {
    let level: LogLevel;
    if (severity === 'high') {
      level = LogLevel.ERROR;
    } else if (severity === 'medium') {
      level = LogLevel.WARN;
    } else {
      level = LogLevel.INFO;
    }

    const message = `Security event: ${eventType}`;
    const logMetadata = {
      operation: `SECURITY:${eventType}`,
      userId,
      severity,
      ...metadata,
    };

    switch (level) {
      case LogLevel.ERROR:
        this.error(message, undefined, logMetadata);
        break;
      case LogLevel.WARN:
        this.warn(message, logMetadata);
        break;
      case LogLevel.INFO:
        this.info(message, logMetadata);
        break;
      default:
        // Fallback for any unexpected log level values
        this.info(message, logMetadata);
        break;
    }
  }

  /**
   * 创建子日志器（带上下文）
   */
  child(context: Record<string, any>): StructuredLogger {
    const childLogger = new StructuredLogger(this.serviceName);
    childLogger.currentLogLevel = this.currentLogLevel;

    // 重写日志方法以包含上下文
    const originalCreateLogEntry = childLogger.createLogEntry.bind(childLogger);
    childLogger.createLogEntry = (
      level: LogLevel,
      message: string,
      metadata?: Record<string, any>,
    ) => originalCreateLogEntry(level, message, { ...context, ...metadata });

    return childLogger;
  }
}

// 导出默认日志器实例
export const logger = new StructuredLogger();

// 导出便捷方法
export const log = {
  error: logger.error.bind(logger),
  warn: logger.warn.bind(logger),
  info: logger.info.bind(logger),
  debug: logger.debug.bind(logger),
  trace: logger.trace.bind(logger),
  requestStart: logger.requestStart.bind(logger),
  requestComplete: logger.requestComplete.bind(logger),
  requestFailed: logger.requestFailed.bind(logger),
  databaseOperation: logger.databaseOperation.bind(logger),
  externalServiceCall: logger.externalServiceCall.bind(logger),
  businessEvent: logger.businessEvent.bind(logger),
  performance: logger.performance.bind(logger),
  securityEvent: logger.securityEvent.bind(logger),
};
