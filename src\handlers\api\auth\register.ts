import 'dotenv/config';
import { APIGatewayEvent, Hand<PERSON> } from 'aws-lambda';
// import { v4 as uuidv4 } from 'uuid';

import { checkPhoneExists, getUserByPhone } from '@lib/user';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { validateRequest, CommonRules } from '@shared/middleware/validation';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { generateAccessToken } from '@src/shared/utils/auth';
import { ErrorCode } from '@shared/types/response';

// 定义注册验证规则
const registerValidationSchema = {
  phone: CommonRules.phone,
  code: {
    required: true,
    type: 'string' as const,
    minLength: 4,
    maxLength: 6,
  },
};

const registerHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  try {
    // 输入验证
    const validator = validateRequest(registerValidationSchema);
    const { phone, code } = validator(event);
    // 检查手机号是否已存在
    const existingPhone = await checkPhoneExists(phone);
    if (existingPhone) {
      log.securityEvent('DUPLICATE_REGISTRATION_ATTEMPT', undefined, 'low', {
        phone,
        requestId,
      });
      const user = await getUserByPhone(phone);
      if (user.code === code) {
        const token = await generateAccessToken(user.userId);
        return ResponseWrapper.success(
          {
            code: 200,
            userId: user.userId,
            token,
          },
          requestId,
        );
      }
      return ResponseWrapper.error(
        ErrorCode.INVALID_CREDENTIALS,
        '验证码不正确',
        undefined,
        requestId,
      );
    }
    return ResponseWrapper.error(
      ErrorCode.INVALID_CREDENTIALS,
      '请联系管理员，开通账号',
      undefined,
      requestId,
    );

    // 创建用户
    // const userId = uuidv4();
    //   const hashedPassword = await hashPassword(password);

    // 生成访问令牌
    // const token = await generateAccessToken(userId);
    // if (!token) {
    //   log.error('Token generation failed', new Error('Token generation failed'), {
    //     userId,
    //     requestId,
    //   });
    //   return ResponseWrapper.internalError('令牌生成失败', undefined, requestId);
    // }
    // await createUser({
    //   userId,
    //   phone,
    // });
    // return ResponseWrapper.success(
    //   {
    //     code: 200,
    //     userId,
    //     token,
    //     message: '注册成功',
    //   },
    //   requestId,
    // );
  } catch (error) {
    log.error('User registration failed', error as Error, {
      requestId,
    });
    return ResponseWrapper.error(ErrorCode.INTERNAL_ERROR, '注册失败', undefined, requestId);
  }
};

export const handler: Handler = globalErrorHandler(registerHandler);
