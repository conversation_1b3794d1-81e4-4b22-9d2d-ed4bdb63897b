import 'dotenv/config';

import { SQSEvent } from 'aws-lambda';
import { deleteMessage } from '@infrastructure/messaging/sqs';
import { log } from '@shared/utils/structuredLogger';
import {
  validatePayload,
  validateFileAndStatus,
  shouldProcessFile,
  validateEnvironment,
} from './validator';
import { createFileProcessor } from './processor';
import { FileProcessingContext } from './types';
import { NoValidContentError, InvalidPayloadError, FileNotFoundError } from './error/errors';

/**
 * 处理单个SQS消息
 * @param sqsMessage SQS消息记录
 */
async function processSQSMessage(sqsMessage: any): Promise<void> {
  const payloadStr = sqsMessage.body || '{}';

  // 删除 SQS 消息
  try {
    await deleteMessage(process.env.FILE_INDEX_QUEUE!, sqsMessage.receiptHandle);
  } catch (error) {
    log.error('Error deleting SQS message', error as Error);
  }

  // 验证载荷
  const payload = validatePayload(payloadStr);
  const { fileId, userId, personaId, s3Key, fileName } = payload;

  log.businessEvent('FILE_INDEX_STARTED', userId, {
    fileId,
    fileName,
    personaId,
  });

  // 验证文件和状态
  const file = await validateFileAndStatus(fileId);

  // 检查是否需要处理
  if (!shouldProcessFile(file, userId)) {
    return;
  }

  // 创建处理上下文
  const context: FileProcessingContext = {
    fileId,
    userId,
    personaId,
    s3Key,
    fileName,
    config: {
      minTextLength: 0,
      embeddingBatchSize: 0,
      embeddingParallelCount: 0,
    }, // 将在处理器中设置
  };

  // 创建并执行处理器
  const processor = createFileProcessor(context);
  await processor.process();
}

/**
 * 文件索引处理 Lambda handler
 * 处理文件的OCR识别、向量化和图片生成
 */
export async function handler(event: SQSEvent): Promise<void> {
  // 验证环境配置
  try {
    validateEnvironment();
  } catch (error) {
    log.error('Environment validation failed', error as Error);
    throw error;
  }

  // 处理所有SQS消息
  const results = await Promise.allSettled(
    event.Records.map(async (sqsMessage) => {
      try {
        await processSQSMessage(sqsMessage);
        return { success: true };
      } catch (error) {
        log.error('Failed to process SQS message', error as Error, {
          messageId: sqsMessage.messageId,
          receiptHandle: sqsMessage.receiptHandle,
        });

        // 根据错误类型决定是否重试
        if (error instanceof InvalidPayloadError || error instanceof FileNotFoundError) {
          // 这些错误不应该重试
          log.warn('Message will not be retried due to permanent error', {
            errorType: error.constructor.name,
            messageId: sqsMessage.messageId,
          });
          return { success: false, permanent: true };
        }

        if (error instanceof NoValidContentError) {
          // 无有效内容不是错误，标记为成功
          log.info('File processed but no valid content found', {
            messageId: sqsMessage.messageId,
          });
          return { success: true };
        }

        // 其他错误可以重试
        return { success: false, permanent: false };
      }
    }),
  );

  // 统计处理结果
  const successful = results.filter(
    (result) => result.status === 'fulfilled' && result.value.success,
  ).length;
  const failed = results.length - successful;

  log.info('Batch processing completed', {
    total: results.length,
    successful,
    failed,
  });

  // 如果有失败的消息，记录详细信息
  if (failed > 0) {
    const failures = results
      .map((result, index) => ({ result, index }))
      .filter(
        ({ result }) =>
          result.status === 'rejected' ||
          (result.status === 'fulfilled' && !result.value.success && !result.value.permanent),
      );

    log.warn('Some messages failed processing', {
      failedCount: failures.length,
      retryableFailures: failures.filter(
        ({ result }) => result.status === 'fulfilled' && !result.value.permanent,
      ).length,
    });
  }
}
