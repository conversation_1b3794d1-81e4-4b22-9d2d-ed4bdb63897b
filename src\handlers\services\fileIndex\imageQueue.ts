import { sendMessage } from '@infrastructure/messaging/sqs';
import { log } from '@shared/utils/structuredLogger';
import { IFilePage } from '@core/types/interface';

/**
 * 图片生成队列消息接口
 */
interface ImageQueueMessage {
  fileId: string;
  pages: number[];
  userId: string;
  s3Key: string;
}

/**
 * 图片队列管理器
 */
export class ImageQueueManager {
  private static readonly PAGE_SIZE = 10; // 每批处理的页面数量

  private readonly queueUrl: string;

  constructor() {
    this.queueUrl = process.env.FILE_PAGE_IMG_QUEUE!;
    if (!this.queueUrl) {
      throw new Error('FILE_PAGE_IMG_QUEUE environment variable is required');
    }
  }

  /**
   * 将页面分组用于批量处理
   * @param pages 页面列表
   * @returns 分组后的页面数组
   */
  private static groupPages(pages: number[]): number[][] {
    const groups: number[][] = [];

    for (let i = 0; i < pages.length; i += ImageQueueManager.PAGE_SIZE) {
      const group = pages.slice(i, i + ImageQueueManager.PAGE_SIZE);
      groups.push(group);
    }

    return groups;
  }

  /**
   * 创建图片生成消息
   * @param fileId 文件ID
   * @param pages 页面数组
   * @param userId 用户ID
   * @param s3Key S3键
   * @returns 消息对象
   */
  private static createMessage(
    fileId: string,
    pages: number[],
    userId: string,
    s3Key: string,
  ): ImageQueueMessage {
    return {
      fileId,
      pages,
      userId,
      s3Key,
    };
  }

  /**
   * 发送图片生成消息到队列
   * @param filePageDocs 文件页面文档
   * @param fileId 文件ID
   * @param userId 用户ID
   * @param s3Key S3键
   * @returns 发送的消息组数量
   */
  async queueImageGeneration(
    filePageDocs: IFilePage[],
    fileId: string,
    userId: string,
    s3Key: string,
  ): Promise<number> {
    if (filePageDocs.length === 0) {
      log.info('No pages to queue for image generation', { fileId, userId });
      return 0;
    }

    // 提取页面号
    const pageNumbers = filePageDocs
      .map((doc) => doc?.page || 0)
      .filter((page) => page > 0)
      .sort((a, b) => a - b);

    if (pageNumbers.length === 0) {
      log.warn('No valid page numbers found', { fileId, userId });
      return 0;
    }

    // 分组页面
    const pageGroups = ImageQueueManager.groupPages(pageNumbers);

    log.info('Queueing image generation', {
      fileId,
      userId,
      totalPages: pageNumbers.length,
      groups: pageGroups.length,
      pageSize: ImageQueueManager.PAGE_SIZE,
    });

    try {
      // 并行发送所有消息组
      await Promise.all(
        pageGroups.map(async (pages, index) => {
          const message = ImageQueueManager.createMessage(fileId, pages, userId, s3Key);
          const messageBody = JSON.stringify(message);

          await sendMessage(messageBody, this.queueUrl);

          log.debug('Image generation message sent', {
            fileId,
            userId,
            groupIndex: index,
            pages,
            messageId: `${fileId}_img_${index}`,
          });
        }),
      );

      log.businessEvent('FILE_IMAGE_QUEUE_COMPLETED', userId, {
        fileId,
        totalPages: pageNumbers.length,
        messageGroups: pageGroups.length,
      });

      return pageGroups.length;
    } catch (error) {
      log.error('Failed to queue image generation messages', error as Error, {
        fileId,
        userId,
        totalPages: pageNumbers.length,
        groups: pageGroups.length,
      });
      throw error;
    }
  }

  /**
   * 获取页面分组信息（用于调试和监控）
   * @param pageCount 总页面数
   * @returns 分组信息
   */
  static getGroupingInfo(pageCount: number): {
    totalGroups: number;
    pageSize: number;
    lastGroupSize: number;
  } {
    const totalGroups = Math.ceil(pageCount / ImageQueueManager.PAGE_SIZE);
    const lastGroupSize = pageCount % ImageQueueManager.PAGE_SIZE || ImageQueueManager.PAGE_SIZE;

    return {
      totalGroups,
      pageSize: ImageQueueManager.PAGE_SIZE,
      lastGroupSize,
    };
  }
}

/**
 * 创建图片队列管理器实例
 * @returns ImageQueueManager实例
 */
export function createImageQueueManager(): ImageQueueManager {
  return new ImageQueueManager();
}
