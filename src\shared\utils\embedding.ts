/* eslint-disable no-await-in-loop */
import { getEmbeddingClient } from './azureAi';
import { EMBEDDING_MAX_TOKENS } from './const';

export interface EmbeddingResponse {
  embedding?: number[];
  index: number;
  object: string;
}
/**
 * 获取文本嵌入向量
 * @param batchText 文本数组
 * @returns 嵌入向量响应
 */
export async function getAzureTextEmbeddings(batchText: string[]): Promise<EmbeddingResponse[]> {
  // 过滤掉空字符串和无效项
  const validBatchText = batchText.filter((text) => typeof text === 'string' && text.trim() !== '');
  // 如果 validBatchText 为空，直接返回空数组
  if (validBatchText.length === 0) {
    return [];
  }

  try {
    const client = getEmbeddingClient();
    const response = await client.embeddings.create({
      input: validBatchText,
      model: 'text-embedding-3-large',
    });
    return response.data;
  } catch (error) {
    console.error('Error getting text embeddings:', error);
    throw error;
  }
}

// 定义 splitTextIntoChunks 函数
export function splitTextIntoChunks(text: string): string[] {
  const approxTokensPerChar = 1.5; // 假设每个字符平均占用 4 个 token
  const maxChars = Math.floor(EMBEDDING_MAX_TOKENS / approxTokensPerChar);
  const chunks = [];
  for (let i = 0; i < text.length; i += maxChars) {
    chunks.push(text.slice(i, i + maxChars));
  }
  return chunks;
}
function averageVectors(vectors: number[][]): number[] {
  if (vectors.length === 0) return [];
  const { length } = vectors[0];
  const result = new Array(length).fill(0);

  vectors.forEach((vec) => {
    vec.forEach((value, i) => {
      result[i] += value;
    });
  });

  return result.map((val) => val / vectors.length);
}
// 提取为可复用的小函数
async function processBatch(
  batchText: { text: string; index: number }[],
  originalDocs: any[],
  targetDocs: any[],
) {
  const texts = batchText.map((item) => item.text);
  const responses = await getAzureTextEmbeddings(texts);

  if (responses.length !== batchText.length) {
    throw new Error(
      `Embedding count mismatch: expected ${batchText.length}, got ${responses.length}`,
    );
  }

  batchText.forEach((item, idx) => {
    // eslint-disable-next-line no-param-reassign
    targetDocs[item.index] = {
      ...originalDocs[item.index],
      embedding: responses[idx].embedding,
    };
  });
}
export async function getAzureTextEmbeddingsInBatches(
  pageDocs: any[],
  mergeStrategy: 'concat' | 'average' = 'average',
) {
  const approxTokensPerChar = 1.5;
  const maxChars = Math.floor(EMBEDDING_MAX_TOKENS / approxTokensPerChar);
  const processedDocs: any[] = new Array(pageDocs.length);

  const getBatchTextLength = (list: { text: string }[]) =>
    list.reduce((acc, item) => acc + item.text.length, 0);

  let batchText: { text: string; index: number }[] = [];

  for (let i = 0; i < pageDocs.length; i += 1) {
    const doc = pageDocs[i];
    const text = doc.content;

    // 如果太长，切 chunk 并合并嵌入
    if (text.length > maxChars) {
      const chunks = splitTextIntoChunks(text);
      const embeddings: number[][] = [];

      // eslint-disable-next-line no-restricted-syntax
      for (const chunk of chunks) {
        const response = await getAzureTextEmbeddings([chunk]);
        if (response.length !== 1 || !response[0].embedding) {
          throw new Error(`未能为 chunk 获取到嵌入向量: ${chunk}`);
        }
        embeddings.push(response[0].embedding);
      }

      const mergedEmbedding =
        mergeStrategy === 'average' ? averageVectors(embeddings) : embeddings.flat();
      if (!Array.isArray(mergedEmbedding) || mergedEmbedding.some((v) => Array.isArray(v))) {
        throw new Error(`第 ${i} 页合并后的 embedding 非一维数组`);
      }
      processedDocs[i] = {
        ...doc,
        embedding: mergedEmbedding,
      };
    } else {
      // 累积构建一个 batch，如果超长了就发请求
      const currentLength = getBatchTextLength(batchText) + text.length;
      if (currentLength > maxChars) {
        await processBatch(batchText, pageDocs, processedDocs);
        batchText = [];
      }
      batchText.push({ text, index: i });
    }
  }

  // 最后一批处理
  if (batchText.length > 0) {
    await processBatch(batchText, pageDocs, processedDocs);
  }

  // 校验 embedding 全部生成
  const missing = processedDocs.map((d, i) => (!d?.embedding ? i : -1)).filter((i) => i >= 0);
  if (missing.length > 0) {
    throw new Error(
      `部分文档未生成 embedding：共 ${missing.length} 条，索引为：${missing.join(', ')}`,
    );
  }

  return processedDocs;
}
