import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { deleteSource, getSourceById } from '@lib/source';

// 定义删除信息源验证规则
const deleteSourceValidationSchema = {
  sourceId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
};

const deleteHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);
  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('SOURCE_DELETE_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(deleteSourceValidationSchema);
    const { sourceId } = validator(event);

    // 检查信息源是否存在
    const existingSource = await getSourceById(sourceId);
    if (!existingSource) {
      log.businessEvent('SOURCE_DELETE_NOT_FOUND', userId, {
        requestId,
        sourceId,
      });

      return ResponseWrapper.error(
        ErrorCode.SOURCE_NOT_FOUND,
        '信息源不存在',
        undefined,
        requestId,
      );
    }

    // 验证用户权限 - 只能删除自己的信息源
    if (existingSource.userId !== userId) {
      log.businessEvent('SOURCE_DELETE_UNAUTHORIZED', userId, {
        requestId,
        sourceId,
        sourceUserId: existingSource.userId,
      });

      return ResponseWrapper.error(
        ErrorCode.ACCESS_DENIED,
        '无权限删除此信息源',
        undefined,
        requestId,
      );
    }

    // 删除信息源
    await deleteSource(sourceId);

    log.businessEvent('SOURCE_DELETE_SUCCESS', userId, {
      requestId,
      sourceId,
      sourceName: existingSource.name,
      sourceType: existingSource.type,
    });

    return ResponseWrapper.success(
      {
        message: '信息源删除成功',
        sourceId,
      },
      requestId,
    );
  } catch (error) {
    log.error('Source deletion failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.SOURCE_DELETE_FAILED,
      `删除信息源失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(deleteHandler);
