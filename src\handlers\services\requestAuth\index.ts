import 'dotenv/config';

import { APIGatewayProxyEvent } from 'aws-lambda';
import { getTokenFromHeader, verifyToken } from '@shared/utils/verifyToken';

export interface Authorizer {
  errName?: string;
  errMessage?: string;
  authenticated?: boolean;
  user?: {
    userId: string;
  };
}
export interface AuthResponseType {
  isAuthorized: boolean;
  context: Authorizer;
}
export const handler = async (event: APIGatewayProxyEvent) => {
  const response: AuthResponseType = {
    isAuthorized: false, // API Gateway check it
    context: {
      authenticated: false,
    },
  };
  try {
    const token = getTokenFromHeader(event);
    if (!token) {
      response.isAuthorized = false;
      response.context.authenticated = false;
      response.context.errName = 'Unauthorized';
      response.context.errMessage = '未提供 token';
      return response;
    }

    const payload = verifyToken(token);
    console.log('payload', payload);
    if (!payload || !payload.userId) {
      response.isAuthorized = false;
      response.context.authenticated = false;
      response.context.errName = 'Unauthorized';
      response.context.errMessage = 'token 无效';
      return response;
    }
    response.isAuthorized = true;
    response.context.authenticated = true;
    response.context.user = {
      userId: payload.userId,
    };
    // ✅ 已验证
    console.log('response', response);
    return response;
  } catch (error) {
    const { name, message } = error as Error;
    console.error('[requestAuth] 验证失败：', error);
    response.isAuthorized = false;
    response.context.authenticated = false;
    response.context.errName = name;
    response.context.errMessage = message;
    return response;
  }
};
