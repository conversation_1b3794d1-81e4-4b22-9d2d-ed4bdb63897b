import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { getFilesByUserAndPersona } from '@lib/file';

// 定义验证规则
const listValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
  },
  limit: {
    required: false,
    type: 'number' as const,
    min: 1,
    max: 100,
  },
  lastEvaluatedKey: {
    required: false,
    type: 'string' as const,
  },
};

/**
 * 获取文件列表接口
 * 支持按人设ID筛选，返回全部数据
 */
const listHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('FILE_LIST_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(listValidationSchema);
    const validatedData = validator(event);

    const { personaId } = validatedData;
    // limit 和 lastEvaluatedKey 参数已废弃，函数现在返回全部数据
    const limit = validatedData.limit || 20; // 保留用于日志记录
    const lastEvaluatedKey = validatedData.lastEvaluatedKey
      ? JSON.parse(validatedData.lastEvaluatedKey)
      : undefined; // 保留用于日志记录

    log.businessEvent('FILE_LIST_PROCESSING', userId, {
      requestId,
      personaId,
      limit, // 仅用于日志，实际返回全部数据
      hasLastKey: !!lastEvaluatedKey, // 仅用于日志
    });

    // 获取文件列表（返回全部数据）
    const result = await getFilesByUserAndPersona(userId, personaId);

    log.businessEvent('FILE_LIST_COMPLETED', userId, {
      requestId,
      personaId,
      fileCount: result.count,
      hasMore: false, // 现在返回全部数据，不再有分页
    });

    return ResponseWrapper.success(
      {
        files: result,
        pagination: {
          count: result.count,
          hasMore: false, // 返回全部数据，不再有分页
          lastEvaluatedKey: undefined, // 不再需要分页键
        },
        filters: {
          userId,
          personaId,
          limit, // 保留用于兼容性，但实际返回全部数据
        },
        metadata: {
          generatedAt: new Date().toISOString(),
        },
      },
      requestId,
    );
  } catch (error) {
    log.error('File list failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.FILE_LIST_FAILED,
      `获取文件列表失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(listHandler);
