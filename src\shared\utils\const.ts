import 'dotenv/config';

// 使用环境变量的常量
export const IMG_WIDTH = parseInt(process.env.IMG_WIDTH || '1000', 10);
export const VECTOR_LIMIT = parseInt(process.env.VECTOR_LIMIT || '15', 10);
export const XHS_PAGE_LIMIT = parseInt(process.env.XHS_PAGE_LIMIT || '5', 10);
export const EMBEDDING_MAX_TOKENS = parseInt(process.env.EMBEDDING_MAX_TOKENS || '8192', 10);

// AWS Bedrock 模型标识符
export const BEDROCK_CLAUDE_MODEL_ID = 'us.anthropic.claude-sonnet-4-20250514-v1:0';
// 枚举值
export enum SupportedLangs {
  zh = '中文',
  en = '英语',
  ja = '日本語',
  es = 'Español',
  ar = 'العربية',
  id = 'Bahasa Indonesia',
}

export enum SubscriptionPlan {
  FREE = 'FREE',
  BASIC_MONTH = 'BASIC_MONTH',
}
