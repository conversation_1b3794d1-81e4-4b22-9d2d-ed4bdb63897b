import {
  ApiGatewayManagementApiClient,
  PostToConnectionCommand,
} from '@aws-sdk/client-apigatewaymanagementapi';

interface ISearchWebsocketMessage {
  type: string;
  connectionId: string;
  data: {
    summaryStream?: string;
    status?: string;
    json?: any;
  };
}

const client = new ApiGatewayManagementApiClient({
  endpoint: process.env.WEBSOCKET_ENDPOINT,
});

export async function sendMessageToClient(connectionId: string, data: ISearchWebsocketMessage) {
  const command = new PostToConnectionCommand({
    ConnectionId: connectionId,
    Data: Buffer.from(JSON.stringify(data)),
  });
  try {
    await client.send(command);
  } catch (error) {
    console.log(error);
  }
}
