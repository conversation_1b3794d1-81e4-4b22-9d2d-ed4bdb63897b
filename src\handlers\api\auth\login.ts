import { getUserByEmail } from '@lib/user';
import { generateAccessToken, comparePassword } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ValidationError, UnauthorizedError } from '@shared/utils/errors';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import 'dotenv/config';

const loginHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 验证请求体
  if (!event.body) {
    throw new ValidationError('请求体不能为空');
  }

  let email: string;
  let password: string;
  try {
    ({ email, password } = JSON.parse(event.body));
  } catch (error) {
    throw new ValidationError('请求体格式错误');
  }

  // 验证必填字段
  if (!email || !password) {
    throw new ValidationError('邮箱和密码不能为空');
  }

  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new ValidationError('邮箱格式不正确');
  }

  // 查找用户
  log.businessEvent('LOGIN_ATTEMPT', undefined, { email, requestId });

  const user = await getUserByEmail(email);
  if (!user) {
    log.securityEvent('LOGIN_FAILED_USER_NOT_FOUND', undefined, 'medium', { email, requestId });
    throw new UnauthorizedError('用户名或密码错误');
  }

  // 检查用户是否已验证
  if (!user.isVerified) {
    log.securityEvent('LOGIN_FAILED_UNVERIFIED', user.userId, 'low', { email, requestId });
    return ResponseWrapper.error(
      ErrorCode.ACCESS_DENIED,
      '账户未验证，请先验证邮箱',
      { needVerification: true },
      requestId,
    );
  }

  // 验证密码
  const isPasswordMatch = await comparePassword(password, user.hashedPassword);
  if (!isPasswordMatch) {
    log.securityEvent('LOGIN_FAILED_WRONG_PASSWORD', user.userId, 'high', { email, requestId });
    throw new UnauthorizedError('用户名或密码错误');
  }

  // 生成访问令牌
  const token = await generateAccessToken(user.userId);
  if (!token) {
    log.error('Token generation failed', new Error('Token generation failed'), {
      userId: user.userId,
      email,
      requestId,
    });
    return ResponseWrapper.internalError('令牌生成失败', undefined, requestId);
  }

  log.businessEvent('LOGIN_SUCCESS', user.userId, { email, requestId });

  // 返回成功响应
  return ResponseWrapper.success(
    {
      token,
      user: {
        userId: user.userId,
        email: user.email,
        isVerified: user.isVerified,
      },
    },
    requestId,
  );
};

export const handler: Handler = globalErrorHandler(loginHandler);
