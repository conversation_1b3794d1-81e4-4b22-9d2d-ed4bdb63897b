import { getPersonaById } from '@infrastructure/database/db/personas';

export const getPersona = async (personaId: string, userId: string) => {
  console.log('Getting persona', JSON.stringify({ personaId, userId }));
  try {
    const persona = await getPersonaById(personaId, userId);
    if (!persona) {
      throw new Error(`人设不存在: ${personaId}`);
    }
    return persona;
  } catch (error) {
    console.error('获取人设信息失败', error);
    throw error;
  }
};
