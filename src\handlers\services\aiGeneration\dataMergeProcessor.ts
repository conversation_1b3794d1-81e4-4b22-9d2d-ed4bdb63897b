// 整合数据队列（向量搜索和外部MCP）

import 'dotenv/config';
import { SQSEvent, SQSRecord } from 'aws-lambda';
import { DataMergeMessage, TaskStatus, ProcessingStage } from '@core/types/generationTask';
import { deleteMessage, sendMessage } from '@infrastructure/messaging/sqs';
import { log } from '@shared/utils/structuredLogger';
import { updateTaskStatus } from '@infrastructure/database/db/generationTasks';
import { generateCandidateContent } from './ai/ai-rag-mcp';

/**
 * 处理单个数据整合任务
 * @param task 任务信息
 */
async function processDataMergeTask(task: DataMergeMessage): Promise<void> {
  const { taskId, userId, personaId, topNResults, mcpData, persona, selectedPhotos, connectionId } =
    task;
  try {
    log.businessEvent('GENERATION_TASK_DATA_MERGE_STARTED', userId, {
      taskId,
      resultCount: topNResults.length,
    });
    const { candidateContent } = await generateCandidateContent(topNResults, mcpData, persona);
    // 发送消息到内容生成队列
    await sendMessage(
      JSON.stringify({
        taskId,
        userId,
        personaId,
        candidateContent,
        selectedPhotos,
        persona,
        connectionId,
      }),
      process.env.CONTENT_GENERATION_QUEUE_URL!,
    );
    // 更新任务状态： 进入内容生成阶段
    await updateTaskStatus(taskId, TaskStatus.PROCESSING, ProcessingStage.CONTENT_GENERATION, 90, {
      candidateContent,
    });
    log.businessEvent('GENERATION_TASK_DATA_MERGE_SUCCESS', userId, {
      taskId,
      candidateCount: candidateContent.length,
    });
  } catch (error) {
    log.error('处理数据整合任务失败', error as Error, {
      taskId,
      userId,
      personaId,
    });
  }
}

/**
 * SQS 事件处理器 - 处理数据整合任务
 * @param event SQS事件
 */
export async function handler(event: SQSEvent): Promise<void> {
  console.log(`Processing ${event.Records.length} data merge tasks`);

  // 并行处理所有任务
  const results = await Promise.allSettled(
    event.Records.map(async (record: SQSRecord) => {
      try {
        // 删除 SQS 消息
        try {
          await deleteMessage(process.env.DATA_MERGE_QUEUE_URL!, record.receiptHandle);
        } catch (error) {
          console.error('Error deleting SQS message:', error);
        }

        const task: DataMergeMessage = JSON.parse(record.body);
        await processDataMergeTask(task);
        return { success: true, taskId: task.taskId };
      } catch (error) {
        console.error('Failed to process SQS record:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    }),
  );

  // 统计处理结果
  const successful = results.filter(
    (result) => result.status === 'fulfilled' && result.value.success,
  ).length;
  const failed = results.length - successful;
  console.log(`Data merge processing completed: ${successful} successful, ${failed} failed`);

  if (failed > 0) {
    console.error(
      'Some data merge tasks failed:',
      results
        .filter((result) => result.status === 'fulfilled' && !result.value.success)
        .map((result) => (result.status === 'fulfilled' ? result.value.error : 'Unknown error')),
    );
  }
}
