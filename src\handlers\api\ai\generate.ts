import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { sendMessage } from '@infrastructure/messaging/sqs';
import { v4 as uuidv4 } from 'uuid';
import {
  TaskStatus,
  ProcessingStage,
  IGenerationTask,
  GenerateTaskMessage,
} from '@core/types/generationTask';
import {
  createGenerationTask,
  updateTaskStatus,
} from '@infrastructure/database/db/generationTasks';

const generateValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
  connectionId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
};

const generateHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);
  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('AI_GENERATE_STARTED', userId, {
      requestId,
    });
    const validator = validateRequest(generateValidationSchema);
    const { personaId, connectionId } = validator(event);

    const taskId = uuidv4(); // 生成任务ID
    // 创建任务记录
    const taskData: IGenerationTask = {
      taskId,
      userId,
      personaId,
      status: TaskStatus.PENDING,
      stage: ProcessingStage.INIT,
      selectedPhotos: [],
      searchKeywords: [],
      topNResults: [],
      mcpData: [],
      thinkingContent: '', // 思考过程
      candidateContent: [], // 拓展数据
      finalContent: [], // 最终生成的内容
      progress: 0, // 预计进度
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    // 发送到队列开始处理
    const message: GenerateTaskMessage = {
      taskId,
      userId,
      personaId,
      connectionId,
    };
    await sendMessage(JSON.stringify(message), process.env.GENERATE_QUEUE_URL!);
    // 创建任务记录
    await createGenerationTask(taskData);
    // 更新任务状态为处理中
    await updateTaskStatus(taskId, TaskStatus.PROCESSING, ProcessingStage.INIT, 5);

    log.businessEvent('AI_GENERATE_SUCCESS', userId, {
      requestId,
      personaId,
      taskId,
    });

    return ResponseWrapper.success(
      {
        message: '生成任务已创建',
        taskId,
      },
      requestId,
    );
  } catch (error) {
    log.error('AI generate failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.INTERNAL_ERROR,
      `生成失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(generateHandler);
