import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { getSourcesByUserAndPersona } from '@lib/source';

// 定义验证规则
const listValidationSchema = {
  personaId: {
    required: false,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
};

const listHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('SOURCE_LIST_REQUESTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(listValidationSchema);
    const { personaId } = validator(event);

    // 获取信息源列表
    const sources = await getSourcesByUserAndPersona(userId, personaId);
    log.businessEvent('SOURCE_LIST_BY_PERSONA_RETRIEVED', userId, {
      requestId,
      personaId,
      count: sources.length,
    });

    return ResponseWrapper.success(
      {
        sources,
        count: sources.length,
      },
      requestId,
    );
  } catch (error) {
    log.error('Source list retrieval failed', error as Error, {
      userId,
      requestId,
    });
    return ResponseWrapper.error(
      ErrorCode.SOURCE_LIST_FAILED,
      `获取信息源列表失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(listHandler);
