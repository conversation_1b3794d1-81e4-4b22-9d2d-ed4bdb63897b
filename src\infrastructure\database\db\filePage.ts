import {
  PutCommand,
  Query<PERSON>ommand,
  UpdateCommand,
  BatchWrite<PERSON>ommand,
  DeleteCommand,
  GetCommand,
} from '@aws-sdk/lib-dynamodb';
import { createHash } from 'crypto';

import { IFilePage } from '@core/types/interface';
import { getDBClient } from './init';

const dbClient = getDBClient();
const TABLE_NAME = 'XHS_FILE_PAGE';

/**
 * 创建文件页面
 * @param data 文件页面数据
 */
export async function createFilePage(data: IFilePage): Promise<void> {
  try {
    const command = new PutCommand({
      TableName: TABLE_NAME,
      Item: data,
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('创建文件页面失败', error);
    throw error;
  }
}

/**
 * 批量创建文件页面
 * @param pages 文件页面数组
 */
export async function batchWriteFilePages(pages: IFilePage[]): Promise<void> {
  if (pages.length === 0) {
    return;
  }

  try {
    // DynamoDB BatchWrite 限制每次最多25个项目
    const BATCH_SIZE = 25;
    const batches = [];

    for (let i = 0; i < pages.length; i += BATCH_SIZE) {
      const batch = pages.slice(i, i + BATCH_SIZE);
      batches.push(batch);
    }

    // 并行处理所有批次
    await Promise.all(
      batches.map(async (batch) => {
        const putRequests = batch.map((page) => ({
          PutRequest: {
            Item: page,
          },
        }));

        const command = new BatchWriteCommand({
          RequestItems: {
            [TABLE_NAME]: putRequests,
          },
        });

        await dbClient.send(command);
      }),
    );

    console.log(`Successfully batch wrote ${pages.length} file pages`);
  } catch (error) {
    console.error('批量创建文件页面失败', error);
    throw error;
  }
}

/**
 * 根据文件ID获取所有页面（返回全部数据）
 * @param fileId 文件ID
 * @param userId 用户ID
 * @returns 文件页面列表
 */
/* eslint-disable no-await-in-loop */
export async function listPagesByFileId(fileId: string, userId: string): Promise<IFilePage[]> {
  try {
    const allPages: IFilePage[] = [];
    let currentLastEvaluatedKey: any;

    // 循环查询直到获取所有数据
    do {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        IndexName: 'fileId-index',
        KeyConditionExpression: 'fileId = :fileId',
        FilterExpression: 'userId = :userId',
        ExpressionAttributeValues: {
          ':fileId': fileId,
          ':userId': userId,
        },
        ExclusiveStartKey: currentLastEvaluatedKey,
      });

      // 需要顺序执行以正确处理分页
      const result = await dbClient.send(command);
      const pages = (result.Items || []) as IFilePage[];

      allPages.push(...pages);
      currentLastEvaluatedKey = result.LastEvaluatedKey;
    } while (currentLastEvaluatedKey);

    return allPages;
  } catch (error) {
    console.error('获取文件页面列表失败', error);
    throw error;
  }
}

/**
 * 更新文件页面
 * @param fileId 文件ID
 * @param page 页码
 * @param updates 更新数据
 */
export async function updateFilePage(
  fileId: string,
  page: number,
  updates: Partial<IFilePage>,
): Promise<void> {
  try {
    const updateExpressions: string[] = [];
    const expressionAttributeNames: Record<string, string> = {};
    const expressionAttributeValues: Record<string, any> = {};

    // 构建更新表达式
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        const attributeName = `#${key}`;
        const attributeValue = `:${key}`;

        updateExpressions.push(`${attributeName} = ${attributeValue}`);
        expressionAttributeNames[attributeName] = key;
        expressionAttributeValues[attributeValue] = value;
      }
    });

    if (updateExpressions.length === 0) {
      return;
    }

    // 使用与创建时相同的ID生成逻辑
    const fileIdHash = createHash('md5').update(fileId).digest('hex').substring(0, 8);
    const pageId = `${fileIdHash}_p${page}`;

    const command = new UpdateCommand({
      TableName: TABLE_NAME,
      Key: {
        id: pageId,
      },
      UpdateExpression: `SET ${updateExpressions.join(', ')}`,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
    });

    await dbClient.send(command);
  } catch (error) {
    console.error('更新文件页面失败', error);
    throw error;
  }
}

/**
 * 根据ID删除文件页面
 * @param pageId 页面ID
 */
export async function deleteFilePageById(pageId: string): Promise<void> {
  try {
    const command = new DeleteCommand({
      TableName: TABLE_NAME,
      Key: {
        id: pageId,
      },
    });

    await dbClient.send(command);
  } catch (error) {
    console.error('删除文件页面失败', error);
    throw error;
  }
}

/**
 * 根据文件ID获取页面统计信息
 * @param fileId 文件ID
 * @param userId 用户ID
 * @returns 页面统计信息
 */
export async function getFilePageStats(
  fileId: string,
  userId: string,
): Promise<{
  totalPages: number;
  pagesWithImages: number;
  pagesWithContent: number;
}> {
  try {
    const pages = await listPagesByFileId(fileId, userId);

    const totalPages = pages.length;
    const pagesWithImages = pages.filter((page) => page.s3Key).length;
    const pagesWithContent = pages.filter(
      (page) => page.content && page.content.trim().length > 0,
    ).length;

    return {
      totalPages,
      pagesWithImages,
      pagesWithContent,
    };
  } catch (error) {
    console.error('获取文件页面统计失败', error);
    throw error;
  }
}

export async function getFilePageById(pageId: string): Promise<IFilePage> {
  try {
    const command = new GetCommand({
      TableName: TABLE_NAME,
      Key: {
        id: pageId,
      },
    });
    const result = await dbClient.send(command);
    return (result.Item as IFilePage) || null;
  } catch (error) {
    console.error('获取文件页面失败', error);
    throw error;
  }
}
