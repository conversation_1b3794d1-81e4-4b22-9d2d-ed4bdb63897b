import { APIGatewayProxyEvent } from 'aws-lambda';
import { ValidationError } from '@shared/utils/errors';
import { log } from '@shared/utils/structuredLogger';

/**
 * 验证规则接口
 */
export interface ValidationRule {
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'email' | 'url' | 'uuid' | 'phone' | 'gender';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
}

/**
 * 验证模式接口
 */
export interface ValidationSchema {
  [key: string]: ValidationRule;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedData: any;
}

/**
 * 输入验证器类
 */
export class InputValidator {
  /**
   * 验证单个字段
   */
  static validateField(
    fieldName: string,
    value: any,
    rule: ValidationRule,
  ): { isValid: boolean; error?: string; sanitizedValue: any } {
    let sanitizedValue = value;

    // 必填验证
    if (rule.required && (value === undefined || value === null || value === '')) {
      return {
        isValid: false,
        error: `${fieldName}是必填字段`,
        sanitizedValue: value,
      };
    }

    // 如果字段不是必填且为空，跳过其他验证
    if (!rule.required && (value === undefined || value === null || value === '')) {
      return { isValid: true, sanitizedValue: value };
    }

    // 类型验证和数据清理
    switch (rule.type) {
      case 'gender': {
        if (value !== 'male' && value !== 'female') {
          return {
            isValid: false,
            error: `${fieldName}必须是male或female`,
            sanitizedValue: value,
          };
        }
        break;
      }
      case 'string': {
        if (typeof value !== 'string') {
          return {
            isValid: false,
            error: `${fieldName}必须是字符串类型`,
            sanitizedValue: value,
          };
        }
        // 清理字符串：去除首尾空格，防止XSS
        sanitizedValue = this.sanitizeString(value);
        break;
      }

      case 'number': {
        const num = Number(value);
        if (Number.isNaN(num)) {
          return {
            isValid: false,
            error: `${fieldName}必须是数字类型`,
            sanitizedValue: value,
          };
        }
        sanitizedValue = num;
        break;
      }

      case 'boolean': {
        if (typeof value !== 'boolean') {
          // 尝试转换常见的布尔值表示
          if (value === 'true' || value === '1' || value === 1) {
            sanitizedValue = true;
          } else if (value === 'false' || value === '0' || value === 0) {
            sanitizedValue = false;
          } else {
            return {
              isValid: false,
              error: `${fieldName}必须是布尔类型`,
              sanitizedValue: value,
            };
          }
        }
        break;
      }

      case 'email': {
        if (typeof value !== 'string') {
          return {
            isValid: false,
            error: `${fieldName}必须是字符串类型`,
            sanitizedValue: value,
          };
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          return {
            isValid: false,
            error: `${fieldName}格式不正确`,
            sanitizedValue: value,
          };
        }
        sanitizedValue = value.toLowerCase().trim();
        break;
      }
      case 'phone': {
        if (typeof value !== 'string') {
          return {
            isValid: false,
            error: `${fieldName}必须是字符串类型`,
            sanitizedValue: value,
          };
        }
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(value)) {
          return {
            isValid: false,
            error: `${fieldName}格式不正确`,
            sanitizedValue: value,
          };
        }
        sanitizedValue = value.trim();
        break;
      }

      case 'url': {
        if (typeof value !== 'string') {
          return {
            isValid: false,
            error: `${fieldName}必须是字符串类型`,
            sanitizedValue: value,
          };
        }
        // Use URL regex pattern for validation instead of constructor
        const urlRegex =
          /^https?:\/\/(?:[-\w.])+(?::[0-9]+)?(?:\/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?)?$/;
        if (!urlRegex.test(value)) {
          return {
            isValid: false,
            error: `${fieldName}不是有效的URL`,
            sanitizedValue: value,
          };
        }
        sanitizedValue = value.trim();
        break;
      }

      case 'uuid': {
        if (typeof value !== 'string') {
          return {
            isValid: false,
            error: `${fieldName}必须是字符串类型`,
            sanitizedValue: value,
          };
        }
        const uuidRegex =
          /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(value)) {
          return {
            isValid: false,
            error: `${fieldName}不是有效的UUID`,
            sanitizedValue: value,
          };
        }
        sanitizedValue = value.toLowerCase();
        break;
      }

      default: {
        // No type validation specified, use value as-is
        sanitizedValue = value;
        break;
      }
    }

    // 长度验证（字符串）
    if (rule.type === 'string' && typeof sanitizedValue === 'string') {
      if (rule.minLength && sanitizedValue.length < rule.minLength) {
        return {
          isValid: false,
          error: `${fieldName}长度不能少于${rule.minLength}个字符`,
          sanitizedValue,
        };
      }
      if (rule.maxLength && sanitizedValue.length > rule.maxLength) {
        return {
          isValid: false,
          error: `${fieldName}长度不能超过${rule.maxLength}个字符`,
          sanitizedValue,
        };
      }
    }

    // 数值范围验证
    if (rule.type === 'number' && typeof sanitizedValue === 'number') {
      if (rule.min !== undefined && sanitizedValue < rule.min) {
        return {
          isValid: false,
          error: `${fieldName}不能小于${rule.min}`,
          sanitizedValue,
        };
      }
      if (rule.max !== undefined && sanitizedValue > rule.max) {
        return {
          isValid: false,
          error: `${fieldName}不能大于${rule.max}`,
          sanitizedValue,
        };
      }
    }

    // 正则表达式验证
    if (rule.pattern && typeof sanitizedValue === 'string') {
      if (!rule.pattern.test(sanitizedValue)) {
        return {
          isValid: false,
          error: `${fieldName}格式不正确`,
          sanitizedValue,
        };
      }
    }

    // 自定义验证
    if (rule.custom) {
      const customResult = rule.custom(sanitizedValue);
      if (customResult !== true) {
        return {
          isValid: false,
          error: typeof customResult === 'string' ? customResult : `${fieldName}验证失败`,
          sanitizedValue,
        };
      }
    }

    return { isValid: true, sanitizedValue };
  }

  /**
   * 验证整个对象
   */
  static validate(data: any, schema: ValidationSchema): ValidationResult {
    const errors: string[] = [];
    const sanitizedData: any = {};

    // 验证每个字段
    Object.entries(schema).forEach(([fieldName, rule]) => {
      const fieldResult = this.validateField(fieldName, data[fieldName], rule);

      if (!fieldResult.isValid) {
        errors.push(fieldResult.error!);
      } else {
        sanitizedData[fieldName] = fieldResult.sanitizedValue;
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData,
    };
  }

  /**
   * 字符串清理：防止XSS攻击
   */
  private static sanitizeString(str: string): string {
    return str
      .trim()
      .replace(/[<>]/g, '') // 移除尖括号
      .replace(/javascript:/gi, '') // 移除javascript:协议
      .replace(/on\w+=/gi, ''); // 移除事件处理器
  }
}

/**
 * 验证中间件
 */
export function validateRequest(schema: ValidationSchema) {
  return (event: APIGatewayProxyEvent) => {
    // 解析请求体
    let requestData: any = {};

    if (event.body) {
      try {
        requestData = JSON.parse(event.body);
      } catch (error) {
        throw new ValidationError('请求体格式错误，必须是有效的JSON');
      }
    }

    // 合并查询参数
    if (event.queryStringParameters) {
      requestData = { ...requestData, ...event.queryStringParameters };
    }

    // 合并路径参数
    if (event.pathParameters) {
      requestData = { ...requestData, ...event.pathParameters };
    }
    log.info('Request data', { query: JSON.stringify(requestData) });
    console.info('Request data', JSON.stringify(requestData));
    // 执行验证
    const result = InputValidator.validate(requestData, schema);
    log.info('Validation result', { result: JSON.stringify(result) });
    console.info('Validation result', JSON.stringify(result));
    if (!result.isValid) {
      throw new ValidationError('输入验证失败', {
        errors: result.errors,
        receivedData: requestData,
      });
    }

    return result.sanitizedData;
  };
}

/**
 * 常用验证规则
 */
export const CommonRules = {
  gender: {
    required: true,
    type: 'gender' as const,
  },
  email: {
    required: true,
    type: 'email' as const,
    maxLength: 255,
  },
  phone: {
    required: true,
    type: 'phone' as const,
    maxLength: 11,
  },
  password: {
    required: true,
    type: 'string' as const,
    minLength: 8,
    maxLength: 128,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, // 至少包含大小写字母和数字
  },
  userId: {
    required: true,
    type: 'uuid' as const,
  },
  pdfId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 200, // 增加长度限制以支持长用户ID
  },
  pageNumber: {
    required: false,
    type: 'number' as const,
    min: 1,
  },
  pageSize: {
    required: false,
    type: 'number' as const,
    min: 1,
    max: 100,
  },
  fileName: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 255,
    pattern: /^[^<>:"/\\|?*]+$/, // 不包含文件系统非法字符
  },
};
