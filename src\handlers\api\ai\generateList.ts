import 'dotenv/config';
import { APIGatewayEvent, Hand<PERSON> } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { createGenerateList } from '@infrastructure/database/db/generateList';
import { IGenerateList } from '@core/types/interface';
import { v4 as uuidv4 } from 'uuid';

export enum GenerateListStatus {
  INIT = 'INIT', // 初始化状态，未发布或未收藏
  FAVORITES = 'FAVORITES',
  PUBLISHED = 'PUBLISHED',
  DELETED = 'DELETED',
}

// 定义生成列表验证规则
const generateListValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
  finalContent: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 10000,
  },
  title: {
    required: false,
    type: 'string' as const,
    maxLength: 200,
  },
  tags: {
    required: false,
    custom: (value: any) => {
      if (value === undefined || value === null) {
        return true; // Optional field
      }
      if (!Array.isArray(value)) {
        return 'tags必须是数组类型';
      }
      if (value.length > 20) {
        return 'tags数组长度不能超过20个元素';
      }
      for (let i = 0; i < value.length; i += 1) {
        if (typeof value[i] !== 'string') {
          return `tags[${i}]必须是字符串类型`;
        }
        if (value[i].length > 50) {
          return `tags[${i}]长度不能超过50个字符`;
        }
      }
      return true;
    },
  },
  style: {
    required: false,
    type: 'string' as const,
    maxLength: 100,
  },
  reasoning: {
    required: false,
    type: 'string' as const,
    maxLength: 1000,
  },
  photos: {
    required: false,
    custom: (value: any) => {
      if (value === undefined || value === null) {
        return true; // Optional field
      }
      if (!Array.isArray(value)) {
        return 'photos必须是数组类型';
      }
      if (value.length > 50) {
        return 'photos数组长度不能超过50个元素';
      }
      for (let i = 0; i < value.length; i += 1) {
        if (typeof value[i] !== 'string') {
          return `photos[${i}]必须是字符串类型`;
        }
        if (value[i].length > 100) {
          return `photos[${i}]长度不能超过100个字符`;
        }
      }
      return true;
    },
  },
  taskId: {
    required: false,
    type: 'string' as const,
    maxLength: 100,
  },
};

const generateListHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);
  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('GENERATE_LIST_CREATE_STARTED', userId, {
      requestId,
    });

    const validator = validateRequest(generateListValidationSchema);
    const { personaId, finalContent, title, tags, style, reasoning, photos, taskId } =
      validator(event);

    // 生成唯一ID
    const id = uuidv4();

    // 构建生成列表数据
    const generateListData: IGenerateList = {
      id,
      userId,
      personaId,
      finalContent,
      title,
      tags,
      style,
      reasoning,
      photos,
      taskId,
      status: GenerateListStatus.INIT,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // 保存到数据库
    await createGenerateList(generateListData);

    log.businessEvent('GENERATE_LIST_CREATE_SUCCESS', userId, {
      requestId,
      generateListId: id,
      taskId,
      contentLength: finalContent.length,
      tagsCount: tags?.length || 0,
      photosCount: photos?.length || 0,
    });

    return ResponseWrapper.success(
      {
        message: '生成列表创建成功',
        id,
        data: generateListData,
      },
      requestId,
    );
  } catch (error) {
    log.error('Generate list creation failed', error as Error, {
      userId,
      requestId,
      errorName: (error as Error).name,
      errorMessage: (error as Error).message,
    });

    return ResponseWrapper.error(
      ErrorCode.INTERNAL_ERROR,
      `创建生成列表失败: ${error instanceof Error ? error.message : '未知错误'}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(generateListHandler);
