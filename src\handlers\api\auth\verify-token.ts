import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';

import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { getTokenFromHeader, verifyToken } from '@shared/utils/verifyToken';
import { getUserById } from '@lib/user';

const verifyTokenHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  try {
    // 从请求头中获取token
    const token = getTokenFromHeader(event);
    if (!token) {
      log.securityEvent('TOKEN_MISSING', undefined, 'medium', {
        requestId,
        userAgent: event.headers['User-Agent'],
        sourceIp: event.requestContext.identity.sourceIp,
      });

      return ResponseWrapper.error('UNAUTHORIZED' as any, '缺少访问令牌', undefined, requestId);
    }

    // 验证token
    const decoded = verifyToken(token);
    if (!decoded || !decoded.userId) {
      log.securityEvent('TOKEN_INVALID', undefined, 'medium', {
        requestId,
        userAgent: event.headers['User-Agent'],
        sourceIp: event.requestContext.identity.sourceIp,
      });

      return ResponseWrapper.error('UNAUTHORIZED' as any, '无效的访问令牌', undefined, requestId);
    }

    // 获取用户信息
    const user = await getUserById(decoded.userId);
    if (!user) {
      log.securityEvent('USER_NOT_FOUND', decoded.userId, 'medium', {
        requestId,
        userId: decoded.userId,
      });

      return ResponseWrapper.error('USER_NOT_FOUND' as any, '用户不存在', undefined, requestId);
    }

    log.businessEvent('TOKEN_VERIFIED', decoded.userId, {
      requestId,
      userId: decoded.userId,
    });

    return ResponseWrapper.success(
      {
        valid: true,
      },
      requestId,
    );
  } catch (error) {
    log.error('Token verification failed', error as Error, {
      requestId,
    });

    // 如果是token相关错误，返回401
    if (
      error instanceof Error &&
      (error.message.includes('jwt') ||
        error.message.includes('token') ||
        error.message.includes('expired'))
    ) {
      return ResponseWrapper.error('UNAUTHORIZED' as any, '令牌验证失败', undefined, requestId);
    }

    return ResponseWrapper.internalError('令牌验证失败', undefined, requestId);
  }
};

export const handler: Handler = globalErrorHandler(verifyTokenHandler);
