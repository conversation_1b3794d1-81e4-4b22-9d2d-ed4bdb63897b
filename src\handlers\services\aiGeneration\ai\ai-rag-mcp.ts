import { IPersona } from '@src/core/types/interface';
import { invokeModelByBedrock } from '@shared/utils/bedrock';
import { BEDROCK_CLAUDE_MODEL_ID } from '@shared/utils/const';
import { getSettingPrompt } from '@src/infrastructure/database/db/setting';

// 向量搜索结果类型
interface VectorSearchResult {
  id: string;
  fileId: string;
  page: number;
  content: string;
  s3Key?: string;
}

// MCP数据类型
interface McpData {
  source: string;
  content: string;
  type: string;
  [key: string]: any;
}

/**
 * 生成候选内容的提示词模板
 */
const createContentPrompt = async (
  vectorResults: VectorSearchResult[],
  mcpData: McpData[],
  persona: IPersona,
): Promise<string> => {
  const vectorContent = vectorResults
    .map((result, index) => `内容${index + 1} (来源: 文档第${result.page}页): ${result.content}`)
    .join('\n');

  const mcpContent = mcpData
    .map((data, index) => `外部数据${index + 1} (来源: ${data.source}): ${data.content}`)
    .join('\n');
  const { rag } = await getSettingPrompt();
  const promptTemplate = rag;

  return promptTemplate
    .replace('{{PERSONA_NAME}}', persona.name)
    .replace('{{PERSONA_GENDER}}', persona.gender)
    .replace('{{PERSONA_MBTI}}', persona.mbti)
    .replace('{{PERSONA_PERSONALITY}}', persona.personality)
    .replace('{{PERSONA_INTRODUCTION}}', persona.introduction)
    .replace('{{PERSONA_TOPICS}}', persona.topics)
    .replace('{{VECTOR_CONTENT}}', vectorContent || '暂无向量搜索结果')
    .replace('{{MCP_CONTENT}}', mcpContent || '暂无外部数据');
};

/**
 * 解析AI返回的候选内容结果
 */
const parseContentResponse = (
  response: string,
): {
  candidateContent: Array<{
    title: string;
    content: string;
    style: string;
  }>;
  summary?: string;
  reasoning?: string;
} => {
  try {
    // 尝试解析JSON格式的响应
    const parsed = JSON.parse(response);

    if (parsed.candidateContent && Array.isArray(parsed.candidateContent)) {
      const candidateContent = parsed.candidateContent.filter(
        (item: any) => item && typeof item === 'object' && item.title && item.content,
      );

      return {
        candidateContent,
        summary: parsed.summary,
        reasoning: parsed.reasoning,
      };
    }
  } catch (error) {
    console.warn('Failed to parse JSON response, attempting text extraction:', error);
  }

  // 如果JSON解析失败，尝试从文本中提取内容
  const lines = response.split('\n').filter((line) => line.trim().length > 0);
  const candidateContent = [];

  for (let i = 0; i < Math.min(lines.length, 5); i += 1) {
    const line = lines[i].trim();
    if (line.length > 10) {
      candidateContent.push({
        title: `候选内容${i + 1}`,
        content: line,
        style: '通用风格',
      });
    }
  }

  return {
    candidateContent:
      candidateContent.length > 0
        ? candidateContent
        : [
            {
              title: '默认内容',
              content: '基于提供的信息生成的内容',
              style: '通用风格',
            },
          ],
  };
};

/**
 * 使用Bedrock生成候选内容
 */
export const generateCandidateContent = async (
  vectorResults: VectorSearchResult[],
  mcpData: McpData[],
  persona: IPersona,
): Promise<{
  candidateContent: Array<{
    title: string;
    content: string;
    style: string;
  }>;
  summary?: string;
  reasoning?: string;
}> => {
  try {
    // 验证输入数据
    if (!vectorResults && !mcpData) {
      throw new Error('向量搜索结果和MCP数据不能同时为空');
    }
    // 生成提示词
    const prompt = await createContentPrompt(vectorResults || [], mcpData || [], persona);

    // 调用Bedrock生成候选内容
    const modelId = BEDROCK_CLAUDE_MODEL_ID;

    console.log('Calling Bedrock for candidate content generation...', {
      modelId,
      vectorResultCount: vectorResults?.length || 0,
      mcpDataCount: mcpData?.length || 0,
      personaName: persona.name,
    });

    const response = await invokeModelByBedrock(modelId, {
      anthropic_version: 'bedrock-2023-05-31',
      max_tokens: 10000,
      temperature: 0.7,
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
    });

    if (!response || response.trim().length === 0) {
      throw new Error('Bedrock返回了空响应');
    }

    // 解析响应
    const { candidateContent, summary, reasoning } = parseContentResponse(response);

    console.log('Generated candidate content successfully:', {
      candidateCount: candidateContent.length,
      hasSummary: !!summary,
      hasReasoning: !!reasoning,
    });

    return {
      candidateContent,
      summary,
      reasoning,
    };
  } catch (error) {
    console.error('生成候选内容失败:', error);

    // 返回备用内容
    const fallbackContent = [
      {
        title: '生活分享',
        content: `作为${persona.name}，我想和大家分享一些关于生活的感悟。${persona.personality}的我，总是能从日常中发现美好。`,
        style: '个人感悟',
      },
      {
        title: '日常记录',
        content: `今天想聊聊日常的话题。${persona.introduction}，希望我的分享能给大家带来一些启发。`,
        style: '日常分享',
      },
    ];

    return {
      candidateContent: fallbackContent,
      summary: '由于AI服务异常，使用了基于人设的备用内容',
      reasoning: `生成失败原因: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  }
};
