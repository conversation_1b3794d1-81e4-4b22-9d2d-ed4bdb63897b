import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { updateSourceConnectionStatus, getSourceById } from '@lib/source';

// 定义验证规则
const updateConnectionValidationSchema = {
  sourceId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
  isConnection: {
    required: true,
    type: 'boolean' as const,
  },
};

const updateConnectionHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('SOURCE_UPDATE_CONNECTION_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(updateConnectionValidationSchema);
    const { sourceId, isConnection } = validator(event);

    // 检查信息源是否存在且属于当前用户
    const existingSource = await getSourceById(sourceId);
    if (!existingSource) {
      log.securityEvent('SOURCE_NOT_FOUND', userId, 'medium', {
        sourceId,
        requestId,
      });
      return ResponseWrapper.error(
        ErrorCode.SOURCE_NOT_FOUND,
        '信息源不存在',
        undefined,
        requestId,
      );
    }

    // 更新连接状态
    await updateSourceConnectionStatus(sourceId, isConnection);

    log.businessEvent('SOURCE_UPDATE_CONNECTION_SUCCESS', userId, {
      requestId,
      sourceId,
      isConnection,
      sourceName: existingSource.name,
    });

    return ResponseWrapper.success(
      {
        message: `信息源连接状态已${isConnection ? '开启' : '关闭'}`,
        sourceId,
        isConnection,
      },
      requestId,
    );
  } catch (error) {
    log.error('Source connection update failed', error as Error, {
      userId,
      requestId,
    });
    return ResponseWrapper.error(
      ErrorCode.SOURCE_UPDATE_FAILED,
      `更新信息源连接状态失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(updateConnectionHandler);
