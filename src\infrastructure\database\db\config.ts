import { PutCommand, UpdateCommand, GetCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { IConfig } from '@core/types/interface';
import { getDBClient } from './init';

const dbClient = getDBClient();
const TABLE_NAME = 'XHS_CONFIG';

export async function createConfig(data: IConfig) {
  try {
    const command = new PutCommand({
      TableName: TABLE_NAME,
      Item: {
        ...data,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('创建配置失败', error);
    throw error;
  }
}

/**
 * 根据配置ID获取配置
 * @param configId 配置ID
 */
export async function getConfigById(configId: string): Promise<IConfig | null> {
  try {
    const command = new GetCommand({
      TableName: TABLE_NAME,
      Key: {
        id: configId,
      },
    });
    const result = await dbClient.send(command);
    return (result.Item as IConfig) || null;
  } catch (error) {
    console.error('获取配置失败', error);
    throw error;
  }
}

/**
 * 根据用户ID和人设ID获取配置 (使用GSI Query，高性能)
 * @param userId 用户ID
 * @param personaId 人设ID
 */
export async function getConfigByUserAndPersona(
  userId: string,
  personaId: string,
): Promise<IConfig | null> {
  try {
    // 使用GSI Query查询，高性能
    const command = new QueryCommand({
      TableName: TABLE_NAME,
      IndexName: 'userId-personaId-index',
      KeyConditionExpression: 'userId = :userId AND personaId = :personaId',
      ExpressionAttributeValues: {
        ':userId': userId,
        ':personaId': personaId,
      },
    });
    const result = await dbClient.send(command);
    return result.Items && result.Items.length > 0 ? (result.Items[0] as IConfig) : null;
  } catch (error) {
    console.error('根据用户和人设获取配置失败', error);
    throw error;
  }
}

/**
 * 更新配置
 * @param configId 配置ID
 * @param updateData 更新数据
 */
export async function updateConfig(
  configId: string,
  updateData: Partial<Pick<IConfig, 'writingStyle' | 'contentLength' | 'writingFields'>>,
): Promise<void> {
  try {
    // 构建更新表达式
    const updateExpressions: string[] = ['#updatedAt = :updatedAt'];
    const expressionAttributeValues: Record<string, any> = {
      ':updatedAt': new Date().toISOString(),
    };
    const expressionAttributeNames: Record<string, string> = {
      '#updatedAt': 'updatedAt',
    };

    // 动态构建更新字段
    Object.entries(updateData).forEach(([key, value]) => {
      if (value !== undefined) {
        const attributeName = `#${key}`;
        const attributeValue = `:${key}`;
        updateExpressions.push(`${attributeName} = ${attributeValue}`);
        expressionAttributeNames[attributeName] = key;
        expressionAttributeValues[attributeValue] = value;
      }
    });

    const command = new UpdateCommand({
      TableName: TABLE_NAME,
      Key: {
        id: configId,
      },
      UpdateExpression: `SET ${updateExpressions.join(', ')}`,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
    });

    await dbClient.send(command);
  } catch (error) {
    console.error('更新配置失败', error);
    throw error;
  }
}

/**
 * 根据用户ID获取所有配置（返回全部数据）
 * @param userId 用户ID
 */
/* eslint-disable no-await-in-loop */
export async function getConfigsByUserId(userId: string): Promise<IConfig[]> {
  try {
    const allConfigs: IConfig[] = [];
    let currentLastEvaluatedKey: any;

    // 循环查询直到获取所有数据
    do {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        IndexName: 'userId-personaId-index',
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: {
          ':userId': userId,
        },
        ExclusiveStartKey: currentLastEvaluatedKey,
      });

      // 需要顺序执行以正确处理分页
      const result = await dbClient.send(command);
      const configs = (result.Items as IConfig[]) || [];

      allConfigs.push(...configs);
      currentLastEvaluatedKey = result.LastEvaluatedKey;
    } while (currentLastEvaluatedKey);

    return allConfigs;
  } catch (error) {
    console.error('根据用户ID获取配置列表失败', error);
    throw error;
  }
}

/**
 * 根据用户ID和人设ID更新配置
 * @param userId 用户ID
 * @param personaId 人设ID
 * @param updateData 更新数据
 */
export async function updateConfigByUserAndPersona(
  userId: string,
  personaId: string,
  updateData: Partial<Pick<IConfig, 'writingStyle' | 'contentLength' | 'writingFields'>>,
): Promise<void> {
  try {
    // 先查找配置
    const existingConfig = await getConfigByUserAndPersona(userId, personaId);
    if (!existingConfig) {
      throw new Error('配置不存在');
    }

    // 使用配置ID进行更新
    await updateConfig(existingConfig.id, updateData);
  } catch (error) {
    console.error('根据用户和人设更新配置失败', error);
    throw error;
  }
}
