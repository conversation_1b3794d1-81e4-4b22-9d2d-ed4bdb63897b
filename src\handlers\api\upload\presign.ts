import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';

import { checkAuth } from '@shared/utils/auth';
import { getMultipartUploadPartUrl } from '@shared/utils/s3';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { validateRequest } from '@shared/middleware/validation';
import { globalErrorHandler, handleExternalServiceError } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';

// 定义预签名验证规则
const presignValidationSchema = {
  uploadId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
  },
  fileKey: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 500,
  },
  partNumber: {
    required: true,
    type: 'number' as const,
    min: 0,
    max: 10000,
  },
};

const presignHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);
  let userId = '';

  try {
    // 身份验证
    userId = checkAuth(event);

    // 输入验证
    const validator = validateRequest(presignValidationSchema);
    const validatedData = validator(event);

    const { uploadId, fileKey, partNumber = 0 } = validatedData;
    log.businessEvent('UPLOAD_PRESIGN_REQUESTED', userId, {
      fileKey,
      uploadId,
      partNumber,
      requestId,
    });

    // 生成预签名URL，15分钟过期
    const presignUrl = await getMultipartUploadPartUrl(
      process.env.XHS_SOURCE_BUCKET_NAME!,
      fileKey,
      uploadId,
      partNumber,
      60 * 15, // 15分钟
    );

    log.businessEvent('UPLOAD_PRESIGN_GENERATED', userId, {
      fileKey,
      uploadId,
      partNumber,
      requestId,
    });

    return ResponseWrapper.success(
      {
        presignUrl,
        fileKey,
        uploadId,
        partNumber,
        expiresIn: 900, // 15分钟
      },
      requestId,
    );
  } catch (error) {
    log.error('Upload presign failed', error as Error, {
      userId,
      requestId,
    });

    return handleExternalServiceError(error, 'S3');
  }
};

export const handler: Handler = globalErrorHandler(presignHandler);
