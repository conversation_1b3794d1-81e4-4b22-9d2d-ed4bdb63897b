import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { getGenerateListByTaskId } from '@infrastructure/database/db/generateList';

// 定义验证规则
const getGenerateValidationSchema = {
  taskId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
};

/**
 * 根据任务ID获取生成列表接口
 * 使用taskId作为查询条件
 */
const getGenerateHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('GENERATE_LIST_GET_BY_TASK_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(getGenerateValidationSchema);
    const { taskId } = validator(event);

    log.businessEvent('GENERATE_LIST_GET_BY_TASK_PROCESSING', userId, {
      requestId,
      taskId,
    });

    // 根据taskId获取生成列表
    const generateLists = await getGenerateListByTaskId(taskId);

    if (!generateLists || generateLists.length === 0) {
      log.businessEvent('GENERATE_LIST_NOT_FOUND_BY_TASK', userId, {
        requestId,
        taskId,
      });

      return ResponseWrapper.error(
        ErrorCode.PDF_NOT_FOUND,
        '未找到该任务的生成列表',
        undefined,
        requestId,
      );
    }

    log.businessEvent('GENERATE_LIST_GET_BY_TASK_SUCCESS', userId, {
      requestId,
      taskId,
      generateListCount: generateLists.length,
    });

    return ResponseWrapper.success(
      {
        message: '获取生成列表成功',
        taskId,
        generateLists,
        count: generateLists.length,
        metadata: {
          retrievedAt: new Date().toISOString(),
          userId,
        },
      },
      requestId,
    );
  } catch (error) {
    log.error('Generate list retrieval by task failed', error as Error, {
      userId,
      requestId,
      errorName: (error as Error).name,
      errorMessage: (error as Error).message,
    });

    return ResponseWrapper.error(
      ErrorCode.INTERNAL_ERROR,
      `根据任务ID获取生成列表失败: ${error instanceof Error ? error.message : '未知错误'}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(getGenerateHandler);
