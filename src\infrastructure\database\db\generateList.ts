import { PutCommand, GetCommand, QueryCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { IGenerateList } from '@core/types/interface';
import { getDBClient } from './init';

const dbClient = getDBClient();
const TABLE_NAME = 'XHS_GENERATE_LIST';

export async function createGenerateList(data: IGenerateList): Promise<void> {
  try {
    const command = new PutCommand({
      TableName: TABLE_NAME,
      Item: {
        ...data,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt || new Date().toISOString(),
      },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('创建生成列表失败', error);
    throw error;
  }
}

/**
 * 根据ID获取生成列表
 * @param id 生成列表ID
 */
export async function getGenerateListById(id: string): Promise<IGenerateList | null> {
  try {
    const command = new GetCommand({
      TableName: TABLE_NAME,
      Key: { id },
    });
    const result = await dbClient.send(command);
    return (result.Item as IGenerateList) || null;
  } catch (error) {
    console.error('获取生成列表失败', error);
    throw error;
  }
}

/**
 * 根据任务ID获取生成列表（返回全部数据）
 * @param taskId 任务ID
 */
/* eslint-disable no-await-in-loop */
export async function getGenerateListByTaskId(taskId: string): Promise<IGenerateList[]> {
  try {
    const allGenerateLists: IGenerateList[] = [];
    let currentLastEvaluatedKey: any;

    // 循环查询直到获取所有数据
    do {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        IndexName: 'taskId-index', // 需要创建GSI
        KeyConditionExpression: 'taskId = :taskId',
        ExpressionAttributeValues: {
          ':taskId': taskId,
        },
        ExclusiveStartKey: currentLastEvaluatedKey,
        ScanIndexForward: false, // 按创建时间倒序
      });

      // 需要顺序执行以正确处理分页
      const result = await dbClient.send(command);
      const generateLists = (result.Items as IGenerateList[]) || [];

      allGenerateLists.push(...generateLists);
      currentLastEvaluatedKey = result.LastEvaluatedKey;
    } while (currentLastEvaluatedKey);

    return allGenerateLists;
  } catch (error) {
    console.error('根据任务ID获取生成列表失败', error);
    throw error;
  }
}

/**
 * 根据用户ID和人设ID获取生成列表（返回全部数据）
 * @param userId 用户ID
 * @param personaId 人设ID
 * @returns 生成列表和分页信息
 */
/* eslint-disable no-await-in-loop */
export async function getGenerateListsByUserAndPersona(
  userId: string,
  personaId: string,
): Promise<{
  generateLists: IGenerateList[];
  lastEvaluatedKey?: any;
  count: number;
}> {
  try {
    const allGenerateLists: IGenerateList[] = [];
    let currentLastEvaluatedKey: any;

    // 循环查询直到获取所有数据
    do {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        IndexName: 'userId-personaId-index', // 需要创建GSI
        KeyConditionExpression: 'userId = :userId AND personaId = :personaId',
        ExpressionAttributeValues: {
          ':userId': userId,
          ':personaId': personaId,
        },
        ExclusiveStartKey: currentLastEvaluatedKey,
        ScanIndexForward: false, // 按创建时间倒序
      });

      // 需要顺序执行以正确处理分页
      const result = await dbClient.send(command);
      const generateLists = (result.Items as IGenerateList[]) || [];

      allGenerateLists.push(...generateLists);
      currentLastEvaluatedKey = result.LastEvaluatedKey;
    } while (currentLastEvaluatedKey);

    return {
      generateLists: allGenerateLists,
      lastEvaluatedKey: undefined, // 返回全部数据时不需要分页键
      count: allGenerateLists.length,
    };
  } catch (error) {
    console.error('根据用户ID和人设ID获取生成列表失败', error);
    throw error;
  }
}

/**
 * 更新生成列表
 * @param id 生成列表ID
 * @param updates 更新数据
 */
export async function updateGenerateList(
  id: string,
  updates: Partial<IGenerateList>,
): Promise<void> {
  try {
    const updateExpressions: string[] = [];
    const expressionAttributeNames: Record<string, string> = {};
    const expressionAttributeValues: Record<string, any> = {};

    // 构建更新表达式
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined && key !== 'id') {
        updateExpressions.push(`#${key} = :${key}`);
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributeValues[`:${key}`] = value;
      }
    });

    // 添加更新时间
    updateExpressions.push('#updatedAt = :updatedAt');
    expressionAttributeNames['#updatedAt'] = 'updatedAt';
    expressionAttributeValues[':updatedAt'] = new Date().toISOString();

    const command = new UpdateCommand({
      TableName: TABLE_NAME,
      Key: { id },
      UpdateExpression: `SET ${updateExpressions.join(', ')}`,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
    });

    await dbClient.send(command);
  } catch (error) {
    console.error('更新生成列表失败', error);
    throw error;
  }
}
