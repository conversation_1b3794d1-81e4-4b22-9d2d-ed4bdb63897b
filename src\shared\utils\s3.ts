import { Readable } from 'stream';

import {
  CompleteMultipartUploadCommand,
  CreateMultipartUploadCommand,
  DeleteObjectCommand,
  GetObjectCommand,
  HeadObjectCommand,
  PutObjectCommand,
  S3Client,
  UploadPartCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

import logger from './logger';

const s3Client = new S3Client({
  region: process.env.XHS_AWS_REGION!,
});

/**
 * 根据文件扩展名获取正确的 MIME 类型
 * @param fileName 文件名
 * @returns MIME 类型
 */
function getContentTypeFromFileName(fileName: string): string {
  const fileExtension = fileName?.split('.').pop()?.toLowerCase() || 'pdf';

  switch (fileExtension) {
    case 'pdf':
      return 'application/pdf';
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'webp':
      return 'image/webp';
    case 'svg':
      return 'image/svg+xml';
    case 'bmp':
      return 'image/bmp';
    case 'tiff':
    case 'tif':
      return 'image/tiff';
    default:
      return 'application/octet-stream';
  }
}

/**
 * 生成预签名 URL 用于上传文件到 S3
 * @param fileName 文件名
 * @param userId 用户ID
 * @returns 预签名URL和文件键的元组
 */
export async function generatePresignedUrl(
  fileName: string,
  userId: string,
): Promise<{ signedUrl: string; fileKey: string }> {
  if (!userId) {
    return { signedUrl: '', fileKey: '' };
  }
  try {
    const fileKey = `uploads/${userId}/${fileName}`;
    const contentType = getContentTypeFromFileName(fileName);

    const command = new PutObjectCommand({
      Bucket: process.env.XHS_SOURCE_BUCKET_NAME,
      Key: fileKey,
      ContentType: contentType,
    });
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 900 });
    return { signedUrl, fileKey };
  } catch (error) {
    console.error('生成预签名 URL 时出错:', error);
    return { signedUrl: '', fileKey: '' };
  }
}

/**
 * 获取 S3 对象的完整 URL
 * @param fileKey S3 对象的键
 * @returns S3 对象的完整 URL
 */
export function getS3ObjectUrl(fileKey: string): string {
  const bucket = process.env.XHS_SOURCE_BUCKET_NAME;
  const region = process.env.XHS_AWS_REGION;
  return `https://${bucket}.s3.${region}.amazonaws.com/${fileKey}`;
}

/**
 * 上传文件到 S3
 * @param bucket 桶名
 * @param fileKey 文件键
 * @param file 文件
 */
export async function uploadFile(bucket: string, fileKey: string, file: Buffer) {
  const contentType = getContentTypeFromFileName(fileKey);
  try {
    const command = new PutObjectCommand({
      Bucket: bucket,
      Key: fileKey,
      Body: file,
      ContentType: contentType,
    });
    await s3Client.send(command);
  } catch (error) {
    console.error('上传文件到 S3 时出错:', error);
    throw error;
  }
}
// fileExists
export async function fileExists(bucket: string, fileKey: string) {
  const command = new HeadObjectCommand({
    Bucket: bucket,
    Key: fileKey,
  });
  try {
    await s3Client.send(command);
    return true;
  } catch (error) {
    return false;
  }
}
// readFile
export async function readFile(bucket: string, fileKey: string) {
  const command = new GetObjectCommand({
    Bucket: bucket,
    Key: fileKey,
  });
  try {
    const response = await s3Client.send(command);
    return response.Body;
  } catch (error) {
    return false;
  }
}
export async function getObjectBuffer(bucket: string, s3Key: string) {
  try {
    const fileStream = (await readFile(bucket, s3Key)) as Readable;
    return Buffer.concat(await fileStream!.toArray());
  } catch (err) {
    logger.error('Error get s3 object', err);
    throw err;
  }
}
// genReadSignedUrl
export async function genReadSignedUrl(bucket: string, fileKey: string, expiresIn: number) {
  const contentType = getContentTypeFromFileName(fileKey);
  console.info(`genReadSignedUrl contentType: ${contentType}`);
  const command = new GetObjectCommand({
    Bucket: bucket,
    Key: fileKey,
    ResponseContentType: contentType,
  });
  const signedUrl = await getSignedUrl(s3Client, command, { expiresIn });
  return signedUrl;
}

/**
 * createMultipartUpload
 * @param fileKey 文件键
 * @returns 预签名URL和文件键的元组
 */
export async function getMultipartUploadId(
  bucket: string,
  fileName: string,
  contentType: string,
  userId: string,
) {
  const fileKey = `uploads/${userId}/${fileName}`;
  try {
    const command = new CreateMultipartUploadCommand({
      Bucket: bucket,
      Key: fileKey,
      ContentType: contentType,
    });
    const response = await s3Client.send(command);
    const uploadId = response.UploadId;
    return {
      uploadId,
      fileKey,
    };
  } catch (error) {
    console.error('createMultipartUpload 时出错:', error);
    throw error;
  }
}
/**
 * 获取分片的预签名 URL
 * @param bucket 桶名
 * @param fileKey 文件键
 * @param uploadId 上传 ID
 */
export async function getMultipartUploadPartUrl(
  bucket: string,
  fileKey: string,
  uploadId: string,
  partNumber: number,
  expiresIn: number,
) {
  try {
    const command = new UploadPartCommand({
      Bucket: bucket,
      Key: fileKey,
      UploadId: uploadId,
      PartNumber: Number(partNumber),
    });

    const url = await getSignedUrl(s3Client, command, { expiresIn });
    return url;
  } catch (error) {
    console.error('获取分片的预签名 URL 时出错:', error);
    throw error;
  }
}

/**
 * 完成分片上传
 * @param bucket 桶名
 * @param fileKey 文件键
 * @param uploadId 上传 ID
 * @param parts 分片信息数组
 */
export async function completeMultipartUpload(
  bucket: string,
  fileKey: string,
  uploadId: string,
  parts: { PartNumber: number; ETag: string }[],
) {
  try {
    const command = new CompleteMultipartUploadCommand({
      Bucket: bucket,
      Key: fileKey,
      UploadId: uploadId,
      MultipartUpload: {
        Parts: parts,
      },
    });
    const response = await s3Client.send(command);
    return response;
  } catch (error) {
    console.error('完成分片上传时出错:', error);
    throw error;
  }
}

/**
 * 删除S3对象
 * @param bucket 桶名
 * @param fileKey 文件键
 */
export async function deleteObject(bucket: string, fileKey: string): Promise<void> {
  try {
    const command = new DeleteObjectCommand({
      Bucket: bucket,
      Key: fileKey,
    });
    await s3Client.send(command);
  } catch (error) {
    console.error('删除S3对象时出错:', error);
    throw error;
  }
}
