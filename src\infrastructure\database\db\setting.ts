import { GetCommand } from '@aws-sdk/lib-dynamodb';
import logger from '@shared/utils/logger';
import { getDBClient } from './init';

const dbClient = getDBClient();
const TABLE_NAME = 'XHS_SETTING';

async function getSetting(id: string) {
  try {
    const command = new GetCommand({
      TableName: TABLE_NAME,
      Key: { id },
    });
    const result = await dbClient.send(command);
    return result.Item;
  } catch (error) {
    console.error('Error getting setting:', error);
    throw error;
  }
}
interface ISettingPdfIndex {
  embeddingBatchSize: number;
  embeddingParallelCount: number;
  indexPdfFileTimeout: string;
  minTextLength: number;
  pdfVectorCollection: string;
}
export async function getSettingPdfIndex(): Promise<ISettingPdfIndex> {
  try {
    const setting = await getSetting('index');
    return setting as ISettingPdfIndex;
  } catch (error) {
    logger.error('Error getting setting:', error);
    throw error;
  }
}

interface ISettingPrompt {
  generate: string;
  photoKeywords: string;
  rag: string;
  photoSummary: string;
}
export async function getSettingPrompt(): Promise<ISettingPrompt> {
  try {
    const setting = await getSetting('prompt');
    return setting as ISettingPrompt;
  } catch (error) {
    logger.error('Error getting setting:', error);
    throw error;
  }
}
