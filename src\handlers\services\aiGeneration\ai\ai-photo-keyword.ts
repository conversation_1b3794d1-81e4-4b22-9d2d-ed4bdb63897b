import { IPhoto, IPersona } from '@src/core/types/interface';
import { invokeModelByBedrock } from '@shared/utils/bedrock';
import { BEDROCK_CLAUDE_MODEL_ID } from '@shared/utils/const';
import { getSettingPrompt } from '@src/infrastructure/database/db/setting';

/**
 * 生成关键词和筛选照片的提示词模板
 */
const createKeywordPrompt = async (
  photos: Array<{ id: string; summary: string }>,
  persona: IPersona,
  historyContent?: string,
): Promise<string> => {
  const photoDescriptions = photos
    .map((photo, index) => `照片${index + 1} (ID: ${photo.id}): ${photo.summary}`)
    .join('\n');

  const historySection = historyContent ? `\n历史内容参考：\n${historyContent}\n` : '';
  const { photoKeywords } = await getSettingPrompt();
  const promptTemplate = photoKeywords;

  return promptTemplate
    .replace('{{PERSONA_NAME}}', persona.name)
    .replace('{{PERSONA_GENDER}}', persona.gender)
    .replace('{{PERSONA_MBTI}}', persona.mbti)
    .replace('{{PERSONA_PERSONALITY}}', persona.personality)
    .replace('{{PERSONA_INTRODUCTION}}', persona.introduction)
    .replace('{{PERSONA_TOPICS}}', persona.topics)
    .replace('{{PHOTO_DESCRIPTIONS}}', photoDescriptions)
    .replace('{{HISTORY_SECTION}}', historySection);
};

/**
 * 解析AI返回的关键词和筛选照片结果
 */
const parseKeywordResponse = (
  response: string,
  originalPhotos: Array<{ id: string; summary: string }>,
): {
  keywords: string[];
  selectedPhotos: Array<{ id: string; summary: string }>;
  reasoning?: string;
} => {
  try {
    // 尝试解析JSON格式的响应
    const parsed = JSON.parse(response);

    if (parsed.keywords && Array.isArray(parsed.keywords)) {
      const keywords = parsed.keywords.filter((keyword: any) => typeof keyword === 'string');

      // 处理筛选后的照片
      let selectedPhotos = originalPhotos; // 默认使用所有照片

      if (parsed.selectedPhotos && Array.isArray(parsed.selectedPhotos)) {
        // 根据AI返回的照片ID筛选照片
        const selectedPhotoIds = parsed.selectedPhotos.filter((id: any) => typeof id === 'string');
        const filteredPhotos = originalPhotos.filter((photo) =>
          selectedPhotoIds.includes(photo.id),
        );

        // 如果筛选结果不为空，使用筛选结果；否则使用原始照片
        if (filteredPhotos.length > 0) {
          selectedPhotos = filteredPhotos;
        }
      }

      return {
        keywords,
        selectedPhotos,
        reasoning: parsed.reasoning,
      };
    }
  } catch (error) {
    console.warn('Failed to parse JSON response, attempting text extraction:', error);
  }

  // 如果JSON解析失败，尝试从文本中提取关键词
  const keywordMatches = response.match(/["']([^"']+)["']/g);
  if (keywordMatches) {
    const keywords = keywordMatches
      .map((match) => match.replace(/["']/g, ''))
      .filter((keyword) => keyword.length > 0 && keyword.length < 50);

    if (keywords.length > 0) {
      return {
        keywords,
        selectedPhotos: originalPhotos, // 解析失败时使用所有照片
      };
    }
  }

  // 最后的备选方案：按行分割并清理
  const lines = response
    .split('\n')
    .map((line) => line.trim())
    .filter((line) => line.length > 0 && !line.includes(':') && line.length < 50)
    .slice(0, 10);

  return {
    keywords: lines.length > 0 ? lines : ['生活', '分享', '日常'],
    selectedPhotos: originalPhotos, // 备选方案使用所有照片
  };
};

/**
 * 使用Bedrock生成照片关键词
 */
export const generatePhotoKeywords = async (
  photosWithSummary: IPhoto[],
  persona: IPersona,
  historyContent?: string,
): Promise<{
  keywords: string[];
  selectedPhotos: Array<{ id: string; summary: string }>;
  persona: IPersona;
  reasoning?: string;
}> => {
  try {
    // 准备照片数据
    const photos = photosWithSummary.map((photo) => ({
      id: photo.id,
      summary: photo.summary,
    }));

    // 生成提示词
    const prompt = await createKeywordPrompt(photos, persona, historyContent);

    // 调用Bedrock生成关键词
    const modelId = BEDROCK_CLAUDE_MODEL_ID;

    console.log('Calling Bedrock for keyword generation...', {
      modelId,
      photoCount: photos.length,
      personaName: persona.name,
    });

    const response = await invokeModelByBedrock(modelId, {
      anthropic_version: 'bedrock-2023-05-31',
      max_tokens: 10000,
      temperature: 0.7,
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
    });

    if (!response || response.trim().length === 0) {
      throw new Error('Bedrock返回了空响应');
    }

    // 解析响应
    const { keywords, selectedPhotos, reasoning } = parseKeywordResponse(response, photos);

    console.log('Generated keywords successfully:', {
      keywordCount: keywords.length,
      selectedPhotoCount: selectedPhotos.length,
      keywords: keywords.slice(0, 5), // 只记录前5个关键词
      selectedPhotoIds: selectedPhotos.map((p) => p.id).slice(0, 3), // 只记录前3个照片ID
    });

    return {
      keywords,
      selectedPhotos,
      persona,
      reasoning,
    };
  } catch (error) {
    console.error('生成照片关键词失败:', error);

    // 返回备用关键词
    const fallbackKeywords = [
      '生活分享',
      '日常记录',
      persona.topics.split(',')[0]?.trim() || '个人成长',
      persona.personality.includes('活泼') ? '活力' : '温馨',
      '美好时光',
    ].filter(Boolean);

    return {
      keywords: fallbackKeywords,
      selectedPhotos: photosWithSummary.map((photo) => ({
        id: photo.id,
        summary: photo.summary,
      })),
      persona,
      reasoning: `由于AI服务异常，使用了基于人设的备用关键词: ${error instanceof Error ? error.message : '未知错误'}`,
    };
  }
};
