import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { CommonRules, validateRequest } from '@shared/middleware/validation';
import { updatePersona, getPersonaById, setDefaultPersona } from '@lib/personas';

// 定义更新验证规则
const updateValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
  },
  name: {
    required: false,
    type: 'string' as const,
    minLength: 1,
    maxLength: 50,
  },
  gender: CommonRules.gender,
  mbti: {
    required: false,
    type: 'string' as const,
    minLength: 4,
    maxLength: 4,
  },
  personality: {
    required: false,
    type: 'string' as const,
    minLength: 1,
    maxLength: 5000,
  },
  introduction: {
    required: false,
    type: 'string' as const,
    minLength: 1,
    maxLength: 5000,
  },
  topics: {
    required: false,
    type: 'string' as const,
    minLength: 1,
    maxLength: 5000,
  },
  isDefault: {
    required: false,
    type: 'boolean' as const,
  },
};

const updateHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('PERSONA_UPDATE_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(updateValidationSchema);
    const { personaId, ...updateData } = validator(event);

    // 检查身份是否存在且属于当前用户
    const existingPersona = await getPersonaById(personaId, userId);
    if (!existingPersona) {
      log.securityEvent('PERSONA_NOT_FOUND', userId, 'medium', {
        personaId,
        requestId,
      });
      return ResponseWrapper.error(ErrorCode.PERSONA_NOT_FOUND, '身份不存在', undefined, requestId);
    }

    // 由于使用复合主键 (userId, personaId)，如果能查到记录就说明有权限

    // 如果要设置为默认身份，使用专门的函数
    if (updateData.isDefault === true) {
      await setDefaultPersona(personaId, userId);
      log.businessEvent('PERSONA_SET_DEFAULT', userId, {
        personaId,
        requestId,
      });
    } else {
      // 普通更新
      const { isDefault, ...otherUpdateData } = updateData;
      if (Object.keys(otherUpdateData).length > 0) {
        await updatePersona(personaId, userId, otherUpdateData);
      }

      // 如果明确设置 isDefault 为 false
      if (isDefault === false) {
        await updatePersona(personaId, userId, { isDefault: false });
      }
    }

    log.businessEvent('PERSONA_UPDATE_SUCCESS', userId, {
      personaId,
      requestId,
      updatedFields: Object.keys(updateData),
    });

    return ResponseWrapper.success(
      {
        message: '更新成功',
        personaId,
      },
      requestId,
    );
  } catch (error) {
    log.error('Persona update failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.PERSONA_UPDATE_FAILED,
      `更新身份失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(updateHandler);
