import { APIGatewayEvent, Handler } from 'aws-lambda';

import { checkTwitterSessionStatus } from '@lib/twitterSession';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { checkAuth } from '@shared/utils/auth';
import { getRequestId } from '@shared/utils/requestUtils';
import { ResponseWrapper } from '@shared/utils/response';

const twitterStatusHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    // 检查用户是否已连接Twitter
    const sessionStatus = await checkTwitterSessionStatus(userId);

    const response = {
      isConnected: sessionStatus.exists,
      isExpired: sessionStatus.expired,
      twitterUser: sessionStatus.session
        ? {
            id: sessionStatus.session.twitterUserId,
            username: sessionStatus.session.twitterUsername,
            connectedAt: sessionStatus.session.createdAt,
          }
        : null,
    };

    return ResponseWrapper.success(response, requestId);
  } catch (error) {
    console.error('Twitter status error:', error);
    return ResponseWrapper.error(
      'INTERNAL_ERROR' as any,
      '获取Twitter状态失败',
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(twitterStatusHandler);
