import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { getPhotosByUserAndPersona } from '@lib/photo';
import { genReadSignedUrl } from '@shared/utils/s3';

// 定义验证规则
const listValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
  },
  limit: {
    required: false,
    type: 'number' as const,
    min: 1,
    max: 100,
  },
  lastEvaluatedKey: {
    required: false,
    type: 'string' as const,
  },
};

/**
 * 获取照片列表接口
 * 支持按人设ID筛选，支持分页
 */
const listHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('PHOTO_LIST_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(listValidationSchema);
    const validatedData = validator(event);

    const { personaId } = validatedData;
    // limit 和 lastEvaluatedKey 参数已废弃，函数现在返回全部数据
    const limit = validatedData.limit || 20; // 保留用于日志记录
    const lastEvaluatedKey = validatedData.lastEvaluatedKey
      ? JSON.parse(validatedData.lastEvaluatedKey)
      : undefined; // 保留用于日志记录

    log.businessEvent('PHOTO_LIST_PROCESSING', userId, {
      requestId,
      personaId,
      limit, // 仅用于日志，实际返回全部数据
      hasLastKey: !!lastEvaluatedKey, // 仅用于日志
    });

    // 获取照片列表（返回全部数据）
    const result = await getPhotosByUserAndPersona(userId, personaId);

    // 为照片生成预签名 URL
    const bucket = process.env.XHS_SOURCE_BUCKET_NAME!;
    const photosWithUrls = await Promise.all(
      result.photos.map(async (photo) => {
        try {
          // 生成1小时有效期的预签名URL
          const imageUrl = await genReadSignedUrl(bucket, photo.s3Key, 3600);
          return {
            ...photo,
            imageUrl,
          };
        } catch (error) {
          log.error('Failed to generate signed URL for photo', error as Error, {
            userId,
            photoId: photo.id,
            s3Key: photo.s3Key,
            requestId,
          });
          // 如果生成预签名URL失败，返回不带URL的照片信息
          return photo;
        }
      }),
    );

    log.businessEvent('PHOTO_LIST_COMPLETED', userId, {
      requestId,
      personaId,
      photoCount: result.count,
      hasMore: false, // 现在返回全部数据，不再有分页
    });

    return ResponseWrapper.success(
      {
        photos: photosWithUrls,
        pagination: {
          count: result.count,
          hasMore: false, // 返回全部数据，不再有分页
          lastEvaluatedKey: undefined, // 不再需要分页键
        },
        filters: {
          userId,
          personaId,
          limit, // 保留用于兼容性，但实际返回全部数据
        },
      },
      requestId,
    );
  } catch (error) {
    log.error('Photo list failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.PHOTO_LIST_FAILED,
      `获取照片列表失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(listHandler);
