import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { getGenerateListsByUserAndPersona } from '@infrastructure/database/db/generateList';
import { getPhotoById } from '@src/infrastructure/database/db/photo';
import { genReadSignedUrl } from '@src/shared/utils/s3';
import { IGenerateList } from '@core/types/interface';

// 定义响应中的生成列表类型，photos字段包含URL信息
interface IGenerateListWithPhotos extends Omit<IGenerateList, 'photos'> {
  photos?: Array<{
    id: string;
    imageUrl: string;
  }>;
}

// 定义验证规则
const listValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
  limit: {
    required: false,
    type: 'number' as const,
    min: 1,
    max: 100,
  },
  lastEvaluatedKey: {
    required: false,
    type: 'string' as const,
  },
};

/**
 * 获取生成列表接口
 * 支持按人设ID筛选，支持分页
 */
const listHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('GENERATE_LIST_LIST_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(listValidationSchema);
    const validatedData = validator(event);

    const { personaId } = validatedData;
    // limit 和 lastEvaluatedKey 参数已废弃，函数现在返回全部数据
    const limit = validatedData.limit || 20; // 保留用于日志记录
    const lastEvaluatedKey = validatedData.lastEvaluatedKey
      ? JSON.parse(validatedData.lastEvaluatedKey)
      : undefined; // 保留用于日志记录

    log.businessEvent('GENERATE_LIST_LIST_PROCESSING', userId, {
      requestId,
      personaId,
      limit, // 仅用于日志，实际返回全部数据
      hasLastKey: !!lastEvaluatedKey, // 仅用于日志
    });

    // 获取生成列表（返回全部数据）
    const result = await getGenerateListsByUserAndPersona(userId, personaId);

    log.businessEvent('GENERATE_LIST_LIST_COMPLETED', userId, {
      requestId,
      personaId,
      generateListCount: result.count,
      hasMore: false, // 现在返回全部数据，不再有分页
    });
    console.log('result=====>', JSON.stringify(result));

    // 照片需要重新获取链接
    const generateListsWithPhotos: IGenerateListWithPhotos[] = await Promise.all(
      result.generateLists.map(async (generateList) => {
        const photosWithUrls = await Promise.all(
          (generateList.photos || []).map(async (item: any) => {
            const photo: any = await getPhotoById(item.id);
            const imageUrl = await genReadSignedUrl(
              process.env.XHS_SOURCE_BUCKET_NAME!,
              photo.s3Key,
              3600,
            );
            return {
              id: item.id,
              imageUrl,
            };
          }),
        );
        return {
          ...generateList,
          photos: photosWithUrls, // Transform photos to include URLs
        };
      }),
    );

    return ResponseWrapper.success(
      {
        generateLists: generateListsWithPhotos,
        pagination: {
          count: result.count,
          hasMore: false, // 返回全部数据，不再有分页
          lastEvaluatedKey: undefined, // 不再需要分页键
        },
        filters: {
          userId,
          personaId,
          limit, // 保留用于兼容性，但实际返回全部数据
        },
        metadata: {
          generatedAt: new Date().toISOString(),
        },
      },
      requestId,
    );
  } catch (error) {
    log.error('Generate list retrieval failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.INTERNAL_ERROR,
      `获取生成列表失败: ${error instanceof Error ? error.message : '未知错误'}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(listHandler);
