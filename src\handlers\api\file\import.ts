import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { batchCreateFiles } from '@lib/file';
import { sendMessage } from '@infrastructure/messaging/sqs';

// 定义验证规则
const importValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
  },
};

/**
 * 文件导入接口
 * 支持批量导入文件到指定人设
 */
const importHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('FILE_IMPORT_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(importValidationSchema);
    const { personaId } = validator(event);

    // 手动验证 files 数组
    const body = JSON.parse(event.body || '{}');
    const { files } = body;

    if (!Array.isArray(files) || files.length === 0) {
      return ResponseWrapper.error(
        ErrorCode.VALIDATION_ERROR,
        '文件数组不能为空',
        undefined,
        requestId,
      );
    }

    if (files.length > 100) {
      return ResponseWrapper.error(
        ErrorCode.VALIDATION_ERROR,
        '一次最多只能导入100个文件',
        undefined,
        requestId,
      );
    }

    // 验证每个文件对象
    const invalidFiles = files.filter(
      (file: any) =>
        !file ||
        typeof file !== 'object' ||
        !file.fileName ||
        !file.s3Key ||
        typeof file.fileName !== 'string' ||
        typeof file.s3Key !== 'string' ||
        file.fileName.length === 0 ||
        file.s3Key.length === 0,
    );

    if (invalidFiles.length > 0) {
      return ResponseWrapper.error(
        ErrorCode.VALIDATION_ERROR,
        '文件数据格式不正确，每个文件必须包含 fileName 和 s3Key 字段',
        undefined,
        requestId,
      );
    }

    // 验证文件类型（支持PDF)
    const supportedExtensions = ['.pdf'];
    const unsupportedFiles = files.filter((file: any) => {
      const fileName = file.fileName.toLowerCase();
      return !supportedExtensions.some((ext) => fileName.endsWith(ext));
    });

    if (unsupportedFiles.length > 0) {
      return ResponseWrapper.error(
        ErrorCode.VALIDATION_ERROR,
        `不支持的文件类型，支持的格式: ${supportedExtensions.join(', ')}`,
        { unsupportedFiles: unsupportedFiles.map((f: any) => f.fileName) },
        requestId,
      );
    }

    log.businessEvent('FILE_IMPORT_PROCESSING', userId, {
      requestId,
      personaId,
      fileCount: files.length,
    });

    // 批量创建文件记录
    const result = await batchCreateFiles(userId, personaId, files);

    // 为成功创建的文件发送向量处理消息
    const createdFiles = result.details.filter((detail) => detail.status === 'created');
    if (createdFiles.length > 0) {
      try {
        await Promise.all(
          createdFiles.map(async (file) => {
            if (file.fileId && file.s3Key) {
              const message = JSON.stringify({
                fileId: file.fileId,
                userId,
                personaId,
                s3Key: file.s3Key,
                fileName: file.fileName,
              });

              await sendMessage(message, process.env.FILE_INDEX_QUEUE!);

              log.businessEvent('FILE_INDEX_QUEUED', userId, {
                fileId: file.fileId,
                fileName: file.fileName,
                requestId,
              });
            }
          }),
        );
      } catch (error) {
        log.error('Failed to queue file indexing', error as Error, {
          userId,
          personaId,
          requestId,
        });
        // 不阻塞主流程，继续返回成功响应
      }
    }

    log.businessEvent('FILE_IMPORT_COMPLETED', userId, {
      requestId,
      personaId,
      created: result.created,
      skipped: result.skipped,
      errors: result.errors,
      queuedForIndexing: createdFiles.length,
    });

    return ResponseWrapper.success(
      {
        message: '文件导入处理完成',
        summary: {
          total: files.length,
          created: result.created,
          skipped: result.skipped,
          errors: result.errors,
          queuedForIndexing: createdFiles.length,
        },
        details: result.details,
        metadata: {
          personaId,
          supportedFormats: supportedExtensions,
          maxFilesPerBatch: 100,
          processedAt: new Date().toISOString(),
          indexingStatus: createdFiles.length > 0 ? 'queued' : 'none',
        },
        processing: {
          vectorIndexing: createdFiles.length > 0,
          expectedSteps: ['OCR识别', '文本提取', '向量化', '图片生成'],
          estimatedTime: `${createdFiles.length * 2}-${createdFiles.length * 5}分钟`,
        },
      },
      requestId,
    );
  } catch (error) {
    log.error('File import failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.FILE_IMPORT_FAILED,
      `文件导入失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(importHandler);
