import 'dotenv/config';
import { SQSE<PERSON>, SQSRecord } from 'aws-lambda';
import { GenerateTaskMessage, TaskStatus, ProcessingStage } from '@core/types/generationTask';
import { getGenerationTask, updateTaskStatus } from '@infrastructure/database/db/generationTasks';
import { deleteMessage, sendMessage } from '@infrastructure/messaging/sqs';
import { log } from '@shared/utils/structuredLogger';
import { getPersona, getPhotos } from './utils';

/**
 * 处理单个生成任务
 * @param task 任务信息
 */
async function processGenerationTask(task: GenerateTaskMessage): Promise<void> {
  const { taskId, userId, personaId, connectionId } = task;
  console.log('Processing generation task', JSON.stringify(task));
  try {
    log.businessEvent('GENERATION_TASK_STARTED', userId, {
      taskId,
      personaId,
    });
    // 验证任务是否存在
    const generationTask = await getGenerationTask(taskId);
    if (!generationTask) {
      throw new Error(`任务不存在: ${taskId}`);
    }
    // 验证任务状态
    if (generationTask.status !== TaskStatus.PROCESSING) {
      log.warn('任务状态不正确', {
        taskId,
        expectedStatus: TaskStatus.PROCESSING,
        actualStatus: generationTask.status,
      });
      return;
    }
    // 获取到persona和photos 信息，开始生成关键词
    const persona = await getPersona(personaId, userId);
    const photosWithSummary = await getPhotos(userId, personaId);
    // 预留获取历史生成内容
    const historyContent: string = '';

    // 放进关键词和图片生成队列
    await sendMessage(
      JSON.stringify({
        taskId,
        userId,
        personaId,
        photosWithSummary,
        persona,
        historyContent,
        connectionId,
      }),
      process.env.KEYWORD_GENERATION_QUEUE_URL!,
    );

    // 更新任务状态：进入关键词生成阶段
    await updateTaskStatus(taskId, TaskStatus.PROCESSING, ProcessingStage.KEYWORD_GENERATION, 40);
    log.businessEvent('GENERATION_TASK_KEYWORD_GENERATION_QUEUED', userId, {
      taskId,
      photoCount: photosWithSummary.length,
      personaName: persona.name,
    });
  } catch (error) {
    log.error('处理生成任务失败', error as Error, {
      taskId,
      userId,
      personaId,
    });

    // 更新任务状态为失败
    await updateTaskStatus(taskId, TaskStatus.FAILED, undefined, undefined, {
      errorMessage: (error as Error).message,
    });
  }
}

/**
 * SQS 事件处理器 - 处理生成任务
 * @param event SQS事件
 */
export async function handler(event: SQSEvent): Promise<void> {
  console.log(`Processing ${event.Records.length} generation tasks`);

  // 并行处理所有任务
  const results = await Promise.allSettled(
    event.Records.map(async (record: SQSRecord) => {
      try {
        // 删除 SQS 消息
        try {
          await deleteMessage(process.env.GENERATE_QUEUE_URL!, record.receiptHandle);
        } catch (error) {
          console.error('Error deleting SQS message:', error);
        }

        const task: GenerateTaskMessage = JSON.parse(record.body);
        await processGenerationTask(task);
        return { success: true, taskId: task.taskId };
      } catch (error) {
        console.error('Failed to process SQS record:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    }),
  );

  // 统计处理结果
  const successful = results.filter(
    (result) => result.status === 'fulfilled' && result.value.success,
  ).length;
  const failed = results.length - successful;

  console.log(`Generation task processing completed: ${successful} successful, ${failed} failed`);

  if (failed > 0) {
    console.error(
      'Some generation tasks failed:',
      results
        .filter((result) => result.status === 'fulfilled' && !result.value.success)
        .map((result) => (result.status === 'fulfilled' ? result.value.error : 'Unknown error')),
    );
  }
}
