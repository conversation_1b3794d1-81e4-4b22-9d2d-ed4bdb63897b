{"name": "xhs-backend", "version": "1.0.0", "description": "", "main": "index.js", "directories": {"lib": "lib"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "dev": "sls offline start --stage dev --host localhost --httpPort 8080", "test:aws-db": "ts-node src/scripts/test-aws-dynamodb.ts", "deploy:prod": "sls deploy --stage production --region ap-southeast-1 --force", "deploy:fn": "sls deploy --stage dev --region ap-southeast-1 function --function pdfIndex  --force", "batch": "ts-node src/scripts/batchImportPdfs.ts", "batch-img": "ts-node src/scripts/batchPdfToImg.ts", "batch-not-pdf": "ts-node src/scripts/batchNotPdf.ts", "clearPdfPageS3key": "ts-node src/scripts/clearPdfPageS3key.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-apigatewaymanagementapi": "^3.848.0", "@aws-sdk/client-bedrock-runtime": "^3.839.0", "@azure-rest/ai-document-intelligence": "^1.0.0", "@paddle/paddle-node-sdk": "^2.8.0", "@sendgrid/mail": "^8.1.5", "@types/bcryptjs": "^3.0.0", "@types/uuid": "^10.0.0", "@zilliz/milvus2-sdk-node": "^2.3.2", "bcryptjs": "^3.0.2", "dotenv": "^16.4.7", "http-errors": "^2.0.0", "jsonwebtoken": "^9.0.0", "node-cache": "^5.1.2", "openai": "^4.86.2", "p-limit": "^3.1.0", "pdf2pic": "^3.1.4", "uuid": "^11.1.0", "winston": "^3.13.0"}, "devDependencies": {"@aws-sdk/client-dynamodb": "^3.758.0", "@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/client-sqs": "^3.758.0", "@aws-sdk/lib-dynamodb": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@types/aws-lambda": "^8.10.147", "@types/http-errors": "^2.0.4", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.13.9", "@typescript-eslint/eslint-plugin": "^7.4.0", "@typescript-eslint/parser": "^7.4.0", "esbuild": "^0.25.3", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "prettier": "^3.2.5", "serverless": "4.7.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2"}}