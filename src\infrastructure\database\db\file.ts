/* eslint-disable no-await-in-loop */
import {
  PutCommand,
  QueryCommand,
  GetCommand,
  UpdateCommand,
  DeleteCommand,
} from '@aws-sdk/lib-dynamodb';

import { IFile } from '@core/types/interface';
import { StatusEnum } from '@constants/enum';
import { getDBClient } from './init';

const dbClient = getDBClient();
const TABLE_NAME = 'XHS_FILE';

/**
 * 创建文件
 * @param data 文件数据
 */
export async function createFile(data: IFile) {
  try {
    const command = new PutCommand({
      TableName: TABLE_NAME,
      Item: {
        ...data,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt || new Date().toISOString(),
      },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('创建文件失败', error);
    throw error;
  }
}

/**
 * 检查文件是否已存在（根据用户ID、人设ID和文件名）
 * @param userId 用户ID
 * @param personaId 人设ID
 * @param fileName 文件名
 * @returns 是否存在
 */
export async function checkFileExists(
  userId: string,
  personaId: string,
  fileName: string,
): Promise<boolean> {
  try {
    const command = new QueryCommand({
      TableName: TABLE_NAME,
      IndexName: 'userId-index',
      KeyConditionExpression: 'userId = :userId',
      FilterExpression: 'personaId = :personaId AND fileName = :fileName',
      ExpressionAttributeValues: {
        ':userId': userId,
        ':personaId': personaId,
        ':fileName': fileName,
      },
      Limit: 1,
    });

    const result = await dbClient.send(command);
    return (result.Count || 0) > 0;
  } catch (error) {
    console.error('检查文件存在性失败', error);
    throw error;
  }
}

/**
 * 批量创建文件（跳过重名文件）
 * @param userId 用户ID
 * @param personaId 人设ID
 * @param files 文件数据数组
 * @returns 创建结果统计
 */
export async function batchCreateFiles(
  userId: string,
  personaId: string,
  files: Array<{ fileName: string; s3Key: string; size: number; type: string }>,
): Promise<{
  created: number;
  skipped: number;
  errors: number;
  details: Array<{
    fileName: string;
    status: 'created' | 'skipped' | 'error';
    message?: string;
    fileId?: string;
    s3Key?: string;
  }>;
}> {
  // 处理单个文件的函数
  const processFile = async (file: {
    fileName: string;
    s3Key: string;
    size: number;
    type: string;
  }) => {
    try {
      // 检查文件是否已存在
      const exists = await checkFileExists(userId, personaId, file.fileName);

      if (exists) {
        return {
          fileName: file.fileName,
          status: 'skipped' as const,
          message: '文件已存在',
        };
      }

      // 创建文件记录
      const fileData: IFile = {
        id: `${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        userId,
        personaId,
        fileName: file.fileName,
        s3Key: file.s3Key,
        fileSize: file.size,
        fileType: file.type,
        imgStatus: StatusEnum.INIT,
        embeddingStatus: StatusEnum.INIT,
        totalPage: 0, // 初始值，后续处理时更新
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      await createFile(fileData);
      return {
        fileName: file.fileName,
        status: 'created' as const,
        fileId: fileData.id,
        s3Key: file.s3Key,
      };
    } catch (error) {
      console.error(`创建文件失败: ${file.fileName}`, error);
      return {
        fileName: file.fileName,
        status: 'error' as const,
        message: error instanceof Error ? error.message : '未知错误',
      };
    }
  };

  // 并行处理所有文件
  const results = await Promise.allSettled(files.map(processFile));

  // 统计结果
  let created = 0;
  let skipped = 0;
  let errors = 0;
  const details: Array<{
    fileName: string;
    status: 'created' | 'skipped' | 'error';
    message?: string;
    fileId?: string;
    s3Key?: string;
  }> = [];

  results.forEach((result) => {
    if (result.status === 'fulfilled') {
      const detail = result.value;
      details.push(detail);

      switch (detail.status) {
        case 'created':
          created += 1;
          break;
        case 'skipped':
          skipped += 1;
          break;
        case 'error':
          errors += 1;
          break;
        default:
          errors += 1;
          break;
      }
    } else {
      errors += 1;
      details.push({
        fileName: '未知文件',
        status: 'error',
        message: '处理失败',
      });
    }
  });

  console.log(
    `Batch file creation completed: ${created} created, ${skipped} skipped, ${errors} errors for user ${userId}, persona ${personaId}`,
  );

  return {
    created,
    skipped,
    errors,
    details,
  };
}

/**
 * 根据ID获取文件
 * @param fileId 文件ID
 * @param userId 用户ID
 * @returns 文件信息
 */
export async function getFileById(fileId: string): Promise<IFile | null> {
  try {
    const command = new GetCommand({
      TableName: TABLE_NAME,
      Key: {
        id: fileId,
      },
    });

    const result = await dbClient.send(command);
    return result.Item as IFile | null;
  } catch (error) {
    console.error('获取文件失败', error);
    throw error;
  }
}

/**
 * 根据用户ID和人设ID获取文件列表（返回全部数据）
 * @param userId 用户ID
 * @param personaId 人设ID（可选）
 * @returns 文件列表和分页信息
 */
export async function getFilesByUserAndPersona(
  userId: string,
  personaId?: string,
): Promise<{
  files: IFile[];
  lastEvaluatedKey?: any;
  count: number;
}> {
  try {
    const allFiles: IFile[] = [];
    let currentLastEvaluatedKey: any;

    // 循环查询直到获取所有数据
    // eslint-disable-next-line no-await-in-loop
    do {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        IndexName: 'userId-index',
        KeyConditionExpression: 'userId = :userId',
        FilterExpression: 'personaId = :personaId',
        ExpressionAttributeValues: {
          ':userId': userId,
          ':personaId': personaId,
        },
        ExclusiveStartKey: currentLastEvaluatedKey,
        ScanIndexForward: false, // 按创建时间倒序
      });

      // 需要顺序执行以正确处理分页
      const result = await dbClient.send(command);
      const files = (result.Items || []) as IFile[];

      allFiles.push(...files);
      currentLastEvaluatedKey = result.LastEvaluatedKey;
    } while (currentLastEvaluatedKey);

    return {
      files: allFiles,
      lastEvaluatedKey: undefined, // 返回全部数据时不需要分页键
      count: allFiles.length,
    };
  } catch (error) {
    console.error('获取文件列表失败', error);
    throw error;
  }
}

/**
 * 更新文件信息
 * @param fileId 文件ID
 * @param updates 更新数据
 * @param userId 用户ID
 */
export async function updateFile(fileId: string, updates: Partial<IFile>): Promise<void> {
  try {
    const updateExpressions: string[] = [];
    const expressionAttributeNames: Record<string, string> = {};
    const expressionAttributeValues: Record<string, any> = {};

    // 构建更新表达式
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        const attributeName = `#${key}`;
        const attributeValue = `:${key}`;

        updateExpressions.push(`${attributeName} = ${attributeValue}`);
        expressionAttributeNames[attributeName] = key;
        expressionAttributeValues[attributeValue] = value;
      }
    });

    // 添加更新时间
    updateExpressions.push('#updatedAt = :updatedAt');
    expressionAttributeNames['#updatedAt'] = 'updatedAt';
    expressionAttributeValues[':updatedAt'] = new Date().toISOString();

    const command = new UpdateCommand({
      TableName: TABLE_NAME,
      Key: {
        id: fileId,
      },
      UpdateExpression: `SET ${updateExpressions.join(', ')}`,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
    });

    await dbClient.send(command);
  } catch (error) {
    console.error('更新文件失败', error);
    throw error;
  }
}

// deleteFileById
export async function deleteFileById(fileId: string): Promise<void> {
  try {
    const command = new DeleteCommand({
      TableName: TABLE_NAME,
      Key: {
        id: fileId,
      },
    });

    await dbClient.send(command);
    console.log('文件删除成功', fileId);
  } catch (error) {
    console.error('删除文件失败', error);
    throw error;
  }
}
