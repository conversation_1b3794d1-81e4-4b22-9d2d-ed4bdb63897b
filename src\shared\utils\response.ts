import { APIGatewayProxyResult } from 'aws-lambda';
import { v4 as uuidv4 } from 'uuid';
import { ApiResponse, ErrorCode, ErrorMessages, PaginatedResponse } from '../types/response';

/**
 * 响应包装器工具类
 */
export class ResponseWrapper {
  private static readonly API_VERSION = '1.0.0';

  /**
   * 创建成功响应
   */
  static success<T>(data: T, requestId?: string, statusCode: number = 200): APIGatewayProxyResult {
    const response: ApiResponse<T> = {
      success: true,
      data,
      meta: {
        timestamp: Date.now(),
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        requestId: requestId || uuidv4(),
        version: this.API_VERSION,
      },
    };

    return {
      statusCode,
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': response.meta!.requestId,
        'X-API-Version': this.API_VERSION,
      },
      body: JSON.stringify(response),
    };
  }

  /**
   * 创建分页成功响应
   */
  static successWithPagination<T>(
    items: T[],
    total: number,
    page: number,
    pageSize: number,
    requestId?: string,
  ): APIGatewayProxyResult {
    const paginatedData: PaginatedResponse<T> = {
      items,
      total,
      page,
      pageSize,
      hasNext: page * pageSize < total,
      hasPrev: page > 1,
    };

    return this.success(paginatedData, requestId);
  }

  /**
   * 创建错误响应
   */
  static error(
    errorCode: ErrorCode,
    customMessage?: string,
    details?: any,
    requestId?: string,
  ): APIGatewayProxyResult {
    const statusCode = 200;
    const message = customMessage || ErrorMessages[errorCode] || '未知错误';

    const response: ApiResponse = {
      success: false,
      error: {
        code: errorCode,
        message,
        details,
      },
      meta: {
        timestamp: Date.now(),
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        requestId: requestId || uuidv4(),
        version: this.API_VERSION,
      },
    };

    return {
      statusCode,
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': response.meta!.requestId,
        'X-API-Version': this.API_VERSION,
      },
      body: JSON.stringify(response),
    };
  }

  /**
   * 创建验证错误响应
   */
  static validationError(details: any, requestId?: string): APIGatewayProxyResult {
    return this.error(ErrorCode.VALIDATION_ERROR, '输入验证失败', details, requestId);
  }

  /**
   * 创建未授权响应
   */
  static unauthorized(message?: string, requestId?: string): APIGatewayProxyResult {
    return this.error(ErrorCode.UNAUTHORIZED, message, undefined, requestId);
  }

  /**
   * 创建未找到响应
   */
  static notFound(resource: string, requestId?: string): APIGatewayProxyResult {
    return this.error(
      ErrorCode.PDF_NOT_FOUND, // 可以根据资源类型动态选择错误码
      `${resource}未找到`,
      undefined,
      requestId,
    );
  }

  /**
   * 创建内部错误响应
   */
  static internalError(message?: string, details?: any, requestId?: string): APIGatewayProxyResult {
    return this.error(ErrorCode.INTERNAL_ERROR, message, details, requestId);
  }

  /**
   * 从错误对象创建响应
   */
  static fromError(error: Error, requestId?: string): APIGatewayProxyResult {
    // 如果是已知的业务错误
    if (error.name === 'ValidationError') {
      return this.validationError(error.message, requestId);
    }

    if (error.name === 'UnauthorizedError') {
      return this.unauthorized(error.message, requestId);
    }

    if (error.name === 'NotFoundError') {
      return this.notFound(error.message, requestId);
    }

    // 默认为内部错误
    return this.internalError(
      error.message,
      process.env.NODE_ENV === 'development' ? error.stack : undefined,
      requestId,
    );
  }
}
