import 'dotenv/config';
import { ReturnValue, Select } from '@aws-sdk/client-dynamodb';
import {
  GetCommand,
  NativeAttributeValue,
  PutCommand,
  QueryCommand,
  UpdateCommand,
} from '@aws-sdk/lib-dynamodb';

import { IUser } from '@core/types/interface';
import { SubscriptionPlan } from '@shared/utils/const';
import { getDBClient } from './init';

const dbClient = getDBClient();
const TABLE_NAME = 'XHS_USERS';

/**
 * 创建用户
 * @param data 用户数据
 */
export async function createUser(data: IUser) {
  try {
    const command = new PutCommand({
      TableName: TABLE_NAME,
      Item: { ...data, createdAt: Date.now(), updatedAt: Date.now() },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('创建用户失败', error);
    throw error;
  }
}
/**
 * 检查手机号是否存在
 * @param phone 手机号
 * @returns 是否存在
 */
export async function checkPhoneExists(phone: string): Promise<boolean> {
  try {
    const params = {
      TableName: TABLE_NAME,
      IndexName: 'phone-index',
      KeyConditionExpression: 'phone = :phone',
      ExpressionAttributeValues: {
        ':phone': phone,
      },
      Select: Select.COUNT, // 只返回计数，不返回实际数据
      Limit: 1, // 找到一个就够了
    };

    const command = new QueryCommand(params);
    const result = await dbClient.send(command);

    return (result.Count || 0) > 0;
  } catch (error) {
    console.error('检查手机号存在性失败', error);
    throw error;
  }
}

/**
 * 获取用户
 * @param email 邮箱
 * @returns 用户
 */
export async function getUserByEmail(email: string) {
  try {
    const params = {
      TableName: TABLE_NAME,
      IndexName: 'email-index',
      KeyConditionExpression: 'email = :email',
      ExpressionAttributeValues: {
        ':email': email,
      },
    };
    const command = new QueryCommand(params);
    const result = await dbClient.send(command);
    return result.Items?.[0];
  } catch (error) {
    console.error('获取用户失败', error);
    throw error;
  }
}

// 根据手机号获取用户,采取分页加载，避免获取不到的情况
export async function getUserByPhone(phone: string) {
  try {
    let items: IUser[] = [];
    let lastEvaluatedKey: Record<string, any> | undefined;
    do {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        IndexName: 'phone-index',
        KeyConditionExpression: 'phone = :phone',
        ExpressionAttributeValues: {
          ':phone': phone,
        },
        ExclusiveStartKey: lastEvaluatedKey,
      });
      // eslint-disable-next-line no-await-in-loop
      const result = await dbClient.send(command);
      if (result.Items) {
        items = items.concat(result.Items as IUser[]);
      }
      lastEvaluatedKey = result.LastEvaluatedKey;
    } while (lastEvaluatedKey);
    return items[0];
  } catch (error) {
    console.error('获取workspace列表失败', error);
    throw error;
  }
}

/**
 * 获取用户
 * @param userId 用户ID
 * @returns 用户
 */
export async function getUserById(userId: string) {
  try {
    const command = new GetCommand({
      TableName: TABLE_NAME,
      Key: { userId },
    });
    const result = await dbClient.send(command);
    return result.Item as IUser;
  } catch (error) {
    console.error('获取用户失败', error);
    throw error;
  }
}

/**
 * 更新用户
 * @param userId 用户ID
 * @param email 邮箱
 */
export async function updateUser(
  userId: string,
  updateBody: {
    isVerified?: boolean;
    hashedPassword?: string;
    planRole?: SubscriptionPlan;
  },
) {
  const updateExpression = ['#updatedAt = :updatedAt'];
  const expressionAttributeValues: Record<string, NativeAttributeValue> = {
    ':updatedAt': Date.now(),
  };
  const expressionAttributeNames: Record<string, string> = {
    '#updatedAt': 'updatedAt',
  };

  if (updateBody.isVerified !== undefined) {
    updateExpression.push('#isVerified = :isVerified');
    expressionAttributeValues[':isVerified'] = updateBody.isVerified;
    expressionAttributeNames['#isVerified'] = 'isVerified';
  }
  if (updateBody.hashedPassword) {
    updateExpression.push('#hashedPassword = :hashedPassword');
    expressionAttributeValues[':hashedPassword'] = updateBody.hashedPassword;
    expressionAttributeNames['#hashedPassword'] = 'hashedPassword';
  }
  if (updateBody.planRole) {
    updateExpression.push('#planRole = :planRole');
    expressionAttributeValues[':planRole'] = updateBody.planRole;
    expressionAttributeNames['#planRole'] = 'planRole';
  }

  const updateParams = {
    TableName: TABLE_NAME,
    Key: {
      userId,
    },
    UpdateExpression: `SET ${updateExpression.join(', ')}`,
    ExpressionAttributeNames: expressionAttributeNames,
    ExpressionAttributeValues: expressionAttributeValues,
    ReturnValues: ReturnValue.ALL_NEW,
  };
  try {
    const data = await dbClient.send(new UpdateCommand(updateParams));
    return data.Attributes;
  } catch (err) {
    console.error('Error updating user:', err);
    throw err;
  }
}
