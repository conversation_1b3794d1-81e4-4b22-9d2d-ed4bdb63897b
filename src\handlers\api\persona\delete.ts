import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { getPersonaById, deletePersona, getPersonasByUserId, setDefaultPersona } from '@lib/personas';

// 定义删除验证规则
const deleteValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
  },
};

const deleteHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('PERSONA_DELETE_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(deleteValidationSchema);
    const { personaId } = validator(event);

    // 检查身份是否存在且属于当前用户
    const existingPersona = await getPersonaById(personaId, userId);
    if (!existingPersona) {
      log.securityEvent('PERSONA_NOT_FOUND', userId, 'medium', {
        personaId,
        requestId,
      });
      return ResponseWrapper.error(ErrorCode.PERSONA_NOT_FOUND, '身份不存在', undefined, requestId);
    }

    // 获取用户所有身份，检查是否为最后一个身份
    const allPersonas = await getPersonasByUserId(userId);
    if (allPersonas.length <= 1) {
      log.businessEvent('PERSONA_DELETE_REJECTED_LAST_ONE', userId, {
        personaId,
        requestId,
        reason: 'Cannot delete the last persona',
      });
      return ResponseWrapper.error(
        ErrorCode.PERSONA_DELETE_FAILED,
        '不能删除最后一个身份，请至少保留一个身份',
        undefined,
        requestId,
      );
    }

    // 如果删除的是默认身份，需要设置另一个身份为默认
    const wasDefault = existingPersona.isDefault;
    let newDefaultPersonaId: string | null = null;

    if (wasDefault) {
      // 找到第一个不是当前要删除的身份作为新的默认身份
      const otherPersona = allPersonas.find((p) => p.personaId !== personaId);
      if (otherPersona) {
        newDefaultPersonaId = otherPersona.personaId;
        await setDefaultPersona(newDefaultPersonaId, userId);
        log.businessEvent('PERSONA_NEW_DEFAULT_SET', userId, {
          oldDefaultPersonaId: personaId,
          newDefaultPersonaId,
          requestId,
        });
      }
    }

    // 删除身份
    await deletePersona(personaId, userId);

    log.businessEvent('PERSONA_DELETE_SUCCESS', userId, {
      requestId,
      personaId,
      personaName: existingPersona.name,
      wasDefault,
      newDefaultPersonaId,
    });

    return ResponseWrapper.success(
      {
        message: '身份删除成功',
        personaId,
        personaName: existingPersona.name,
        wasDefault,
        newDefaultPersonaId,
      },
      requestId,
    );
  } catch (error) {
    log.error('Persona deletion failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.PERSONA_DELETE_FAILED,
      `删除身份失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(deleteHandler);
