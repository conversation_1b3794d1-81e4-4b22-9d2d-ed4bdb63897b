import { SQSEvent } from 'aws-lambda';
import * as fs from 'fs/promises';
import { fromBuffer } from 'pdf2pic';

import { getFileById, updateFile } from '@lib/file';
import { listPagesByFileId, updateFilePage } from '@lib/filePage';
import { StatusEnum } from '@constants/enum';
import { deleteMessage } from '@infrastructure/messaging/sqs';
import { IMG_WIDTH } from '@shared/utils/const';
import { log } from '@shared/utils/structuredLogger';
import { fileExists, getObjectBuffer, uploadFile } from '@shared/utils/s3';

interface EventBody {
  fileId: string;
  pages: number[];
  userId: string;
  s3Key: string;
}

const bucketName = process.env.XHS_SOURCE_BUCKET_NAME!;

/**
 * 生成文件页面图片
 * @param pdfBuffer PDF文件缓冲区
 * @param fileS3Key 文件S3路径
 * @param pageNum 页码
 * @returns 生成的图片S3路径
 */
async function generatePageImage(
  pdfBuffer: Buffer,
  fileS3Key: string,
  pageNum: number,
): Promise<string> {
  const convert = fromBuffer(pdfBuffer, {
    format: 'png',
    width: IMG_WIDTH,
    density: 100,
    quality: 100,
    preserveAspectRatio: true,
    savePath: '/tmp',
  });

  try {
    const result = await convert(pageNum, {
      responseType: 'image',
    });
    if (!result.path) {
      throw new Error('No path in result');
    }
    const resultBuffer = await fs.readFile(result.path);

    const imageKey = `file-pages/${fileS3Key}/${pageNum}.png`;
    log.info('Generating file page image', {
      imageKey,
      pageNum,
      fileS3Key,
    });

    // 上传到 S3
    await uploadFile(bucketName, imageKey, resultBuffer);

    // 删除临时文件
    await fs.unlink(result.path);

    return imageKey;
  } catch (error) {
    log.error('Error processing file page', error as Error, {
      pageNum,
      fileS3Key,
    });
    return '';
  }
}

/**
 * 文件页面图片生成处理器
 */
export async function handler(event: SQSEvent) {
  log.info('Received request for file page image generation', {
    recordCount: event.Records.length,
  });

  const sqsMessage = event.Records[0];
  const payloadStr = sqsMessage.body || '{}';

  // 删除 SQS 消息
  try {
    await deleteMessage(process.env.FILE_PAGE_IMG_QUEUE!, sqsMessage.receiptHandle);
  } catch (error) {
    log.error('Error deleting SQS message', error as Error);
  }

  let payload: EventBody;
  try {
    payload = JSON.parse(payloadStr);
  } catch (error) {
    log.error('Invalid payload format', error as Error, { payloadStr });
    return;
  }

  const { fileId, pages, userId, s3Key } = payload;

  if (!fileId || !pages || pages.length === 0 || !userId || !s3Key) {
    log.error('Invalid payload');
    return;
  }

  log.info('Processing file page images', {
    fileId,
    pages,
    userId,
    s3Key,
  });

  try {
    // 获取文件信息
    const file = await getFileById(fileId);
    if (!file) {
      throw new Error('File not found');
    }

    // 获取PDF文件缓冲区
    let pdfBuffer: Buffer;
    try {
      pdfBuffer = await getObjectBuffer(bucketName, s3Key);
      if (!pdfBuffer) {
        throw new Error('PDF buffer not found');
      }
    } catch (error) {
      log.error('Failed to get PDF buffer', error as Error, {
        fileId,
        s3Key,
      });
      return;
    }

    log.info('Processing file pages', {
      fileId,
      pageCount: pages.length,
    });

    // 处理所有页面
    await Promise.all(
      pages.map(async (page) => {
        try {
          let imageS3Key = `file-pages/${s3Key}/${page}.png`;

          // 检查图片是否已存在
          const checkoutImgInS3 = await fileExists(bucketName, imageS3Key);
          if (!checkoutImgInS3) {
            imageS3Key = await generatePageImage(pdfBuffer!, s3Key, page);
          }

          // 更新页面记录
          if (imageS3Key) {
            await updateFilePage(fileId, page, { s3Key: imageS3Key });
          }
        } catch (error) {
          log.error('Failed to process file page', error as Error, {
            fileId,
            page,
            userId,
          });
          // 继续处理其他页面
        }
      }),
    );

    // 检查所有页面是否都有图片
    const filePages = await listPagesByFileId(fileId, userId);
    const allPagesHaveImages = filePages.every((filePage) => filePage.s3Key);

    if (allPagesHaveImages) {
      await updateFile(fileId, { imgStatus: StatusEnum.SUCCESS });
      log.businessEvent('FILE_IMG_GENERATION_COMPLETED', userId, {
        fileId,
        totalPages: filePages.length,
      });
    } else {
      log.businessEvent('FILE_IMG_GENERATION_PARTIAL', userId, {
        fileId,
        totalPages: filePages.length,
        pagesWithImages: filePages.filter((p) => p.s3Key).length,
      });
    }
  } catch (error) {
    log.error('File image generation failed', error as Error, {
      fileId,
      userId,
      pages,
    });

    // 更新文件状态为失败
    try {
      await updateFile(fileId, { imgStatus: StatusEnum.FAILED });
    } catch (updateError) {
      log.error('Failed to update file img status to FAILED', updateError as Error, {
        fileId,
        userId,
      });
    }
  }
}
