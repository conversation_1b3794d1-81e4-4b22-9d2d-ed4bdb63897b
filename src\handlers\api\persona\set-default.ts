import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { getPersonaById, setDefaultPersona } from '@lib/personas';

// 定义设置默认身份验证规则
const setDefaultValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
  },
};

const setDefaultHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('PERSONA_SET_DEFAULT_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(setDefaultValidationSchema);
    const { personaId } = validator(event);

    // 检查身份是否存在且属于当前用户
    const existingPersona = await getPersonaById(personaId, userId);
    if (!existingPersona) {
      log.securityEvent('PERSONA_NOT_FOUND', userId, 'medium', {
        personaId,
        requestId,
      });
      return ResponseWrapper.error(ErrorCode.PERSONA_NOT_FOUND, '身份不存在', undefined, requestId);
    }

    // 由于使用复合主键 (userId, personaId)，如果能查到记录就说明有权限

    // 设置为默认身份
    await setDefaultPersona(personaId, userId);

    log.businessEvent('PERSONA_SET_DEFAULT_SUCCESS', userId, {
      personaId,
      requestId,
    });

    return ResponseWrapper.success(
      {
        message: '设置默认身份成功',
        personaId,
      },
      requestId,
    );
  } catch (error) {
    log.error('Set default persona failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.PERSONA_UPDATE_FAILED,
      `设置默认身份失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(setDefaultHandler);
