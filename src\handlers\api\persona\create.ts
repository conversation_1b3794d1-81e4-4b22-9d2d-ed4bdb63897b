import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { CommonRules, validateRequest } from '@shared/middleware/validation';
import { createPersona, getPersonasByUserId } from '@lib/personas';
import { v4 as uuidv4 } from 'uuid';
// 定义注册验证规则
const createValidationSchema = {
  name: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 50,
  },
  gender: CommonRules.gender,
  mbti: {
    required: true,
    type: 'string' as const,
    minLength: 4,
    maxLength: 4,
  },
  personality: {
    required: false,
    type: 'string' as const,
    minLength: 1,
    maxLength: 5000,
  },
  introduction: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 5000,
  },
  topics: {
    required: false,
    type: 'string' as const,
    minLength: 1,
    maxLength: 5000,
  },
};

const createHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);
  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('PERSONA_CREATE_STARTED', userId, {
      requestId,
    });
    const validator = validateRequest(createValidationSchema);
    const { name, gender, mbti, personality, introduction, topics } = validator(event);
    const personaId = uuidv4();
    const personas = await getPersonasByUserId(userId);
    const insertData = {
      userId,
      personaId,
      name,
      gender,
      mbti,
      personality,
      introduction,
      topics,
      isDefault: personas.length === 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    await createPersona(insertData);

    return ResponseWrapper.success(
      {
        message: '创建成功',
        role: insertData,
      },
      requestId,
    );
  } catch (error) {
    return ResponseWrapper.error(
      ErrorCode.PERSONA_CREATE_FAILED,
      `创建失败${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(createHandler);
