// 内容生成队列

import 'dotenv/config';
import { SQSEvent, SQSRecord } from 'aws-lambda';
import { ContentGenerationMessage, TaskStatus, ProcessingStage } from '@core/types/generationTask';
import { deleteMessage } from '@infrastructure/messaging/sqs';
import { log } from '@shared/utils/structuredLogger';
import { updateTaskStatus } from '@infrastructure/database/db/generationTasks';
import {
  // generateContent,
  generateContentWithThinking,
  parseFinalContentResponse,
} from './ai/ai-content-generation';

/**
 * 处理单个内容生成任务
 * @param task 任务信息
 */
async function processContentGenerationTask(task: ContentGenerationMessage): Promise<void> {
  const {
    taskId,
    userId,
    personaId,
    topNResults = [],
    mcpData = [],
    candidateContent,
    persona,
    selectedPhotos,
    connectionId,
  } = task;
  try {
    log.businessEvent('GENERATION_TASK_CONTENT_GENERATION_STARTED', userId, {
      taskId,
      resultCount: topNResults.length,
    });
    const resultTop =
      topNResults.length === 0 && mcpData.length === 0 ? [] : candidateContent || [];

    // const finalContent = await generateContent(resultTop, persona, selectedPhotos);

    await generateContentWithThinking(
      connectionId,
      taskId,
      resultTop,
      persona,
      selectedPhotos,
      {}, // options parameter
      async ({ thinkingContent, finalContent: callbackFinalContent, taskId: callbackTaskId }) => {
        updateTaskStatus(
          callbackTaskId,
          TaskStatus.COMPLETED,
          ProcessingStage.FINAL_GENERATION,
          100,
          {
            finalContent: await parseFinalContentResponse(callbackFinalContent),
            thinkingContent,
          },
        );
      },
    );
  } catch (error) {
    log.error('处理内容生成任务失败', error as Error, {
      taskId,
      userId,
      personaId,
    });
    await updateTaskStatus(taskId, TaskStatus.FAILED, undefined, undefined, {
      errorMessage: '内容生成失败',
    });
  }
}

/**
 * SQS 事件处理器 - 处理内容生成任务
 * @param event SQS事件
 */
export async function handler(event: SQSEvent): Promise<void> {
  console.log(`Processing ${event.Records.length} content generation tasks`);

  // 并行处理所有任务
  const results = await Promise.allSettled(
    event.Records.map(async (record: SQSRecord) => {
      try {
        // 删除 SQS 消息
        try {
          await deleteMessage(process.env.CONTENT_GENERATION_QUEUE_URL!, record.receiptHandle);
        } catch (error) {
          console.error('Error deleting SQS message:', error);
        }

        const task: ContentGenerationMessage = JSON.parse(record.body);
        console.time('processContentGenerationTask');
        await processContentGenerationTask(task);
        console.timeEnd('processContentGenerationTask'); // 结束计时
        return { success: true, taskId: task.taskId };
      } catch (error) {
        console.error('Failed to process SQS record:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    }),
  );

  // 统计处理结果
  const successful = results.filter(
    (result) => result.status === 'fulfilled' && result.value.success,
  ).length;
  const failed = results.length - successful;
  console.log(
    `Content generation processing completed: ${successful} successful, ${failed} failed`,
  );

  if (failed > 0) {
    console.error(
      'Some content generation tasks failed:',
      results
        .filter((result) => result.status === 'fulfilled' && !result.value.success)
        .map((result) => (result.status === 'fulfilled' ? result.value.error : 'Unknown error')),
    );
  }
}
