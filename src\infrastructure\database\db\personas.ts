import {
  PutCommand,
  Query<PERSON>ommand,
  UpdateCommand,
  <PERSON>Command,
  DeleteCommand,
} from '@aws-sdk/lib-dynamodb';

import { IPersona } from '@core/types/interface';
import { getDBClient } from './init';

const dbClient = getDBClient();
const TABLE_NAME = 'XHS_PERSONAS';

/**
 * 创建人设
 * @param data 人设数据
 */
export async function createPersona(data: IPersona) {
  try {
    const command = new PutCommand({
      TableName: TABLE_NAME,
      Item: {
        ...data,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt || new Date().toISOString(),
      },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('创建人设失败', error);
    throw error;
  }
}

/**
 * 获取用户的身份列表
 * @param userId 用户ID
 * @returns 身份列表
 */
export async function getPersonasByUserId(userId: string): Promise<IPersona[]> {
  try {
    let items: IPersona[] = [];
    let lastEvaluatedKey: Record<string, any> | undefined;

    do {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        // 直接查询主表，userId 是分区键
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: {
          ':userId': userId,
        },
        ExclusiveStartKey: lastEvaluatedKey,
      });

      // eslint-disable-next-line no-await-in-loop
      const result = await dbClient.send(command);

      if (result.Items) {
        items = items.concat(result.Items as IPersona[]);
      }

      lastEvaluatedKey = result.LastEvaluatedKey;
    } while (lastEvaluatedKey);

    // 按创建时间倒序排列
    return items.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
  } catch (error) {
    console.error('获取身份列表失败', error);
    throw error;
  }
}

/**
 * 获取单个身份信息
 * @param personaId 身份ID
 * @param userId 用户ID
 * @returns 身份信息
 */
export async function getPersonaById(personaId: string, userId: string): Promise<IPersona | null> {
  try {
    const command = new GetCommand({
      TableName: TABLE_NAME,
      Key: {
        userId, // 分区键
        personaId, // 排序键
      },
    });
    const result = await dbClient.send(command);
    return (result.Item as IPersona) || null;
  } catch (error) {
    console.error('获取身份信息失败', error);
    throw error;
  }
}

/**
 * 更新身份信息
 * @param personaId 身份ID
 * @param userId 用户ID (用于验证权限)
 * @param updateData 更新数据
 */
export async function updatePersona(
  personaId: string,
  userId: string,
  updateData: Partial<
    Pick<
      IPersona,
      'name' | 'gender' | 'mbti' | 'personality' | 'introduction' | 'topics' | 'isDefault'
    >
  >,
): Promise<void> {
  try {
    // 构建更新表达式
    const updateExpressions: string[] = ['#updatedAt = :updatedAt'];
    const expressionAttributeValues: Record<string, any> = {
      ':updatedAt': new Date().toISOString(),
    };
    const expressionAttributeNames: Record<string, string> = {
      '#updatedAt': 'updatedAt',
    };

    // 动态构建更新字段
    Object.entries(updateData).forEach(([key, value]) => {
      if (value !== undefined) {
        const attributeName = `#${key}`;
        const attributeValue = `:${key}`;
        updateExpressions.push(`${attributeName} = ${attributeValue}`);
        expressionAttributeNames[attributeName] = key;
        expressionAttributeValues[attributeValue] = value;
      }
    });

    const command = new UpdateCommand({
      TableName: TABLE_NAME,
      Key: {
        userId, // 分区键
        personaId, // 排序键
      },
      UpdateExpression: `SET ${updateExpressions.join(', ')}`,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
    });

    await dbClient.send(command);
  } catch (error) {
    console.error('更新身份失败', error);
    throw error;
  }
}

/**
 * 设置默认身份（同时取消其他身份的默认状态）
 * @param personaId 要设置为默认的身份ID
 * @param userId 用户ID
 */
export async function setDefaultPersona(personaId: string, userId: string): Promise<void> {
  try {
    // 1. 先获取用户所有身份
    const allPersonas = await getPersonasByUserId(userId);

    // 2. 批量更新：取消所有身份的默认状态，然后设置指定身份为默认
    const updatePromises = allPersonas.map(async (persona) => {
      const isDefault = persona.personaId === personaId;
      return updatePersona(persona.personaId, userId, { isDefault });
    });

    await Promise.all(updatePromises);
  } catch (error) {
    console.error('设置默认身份失败', error);
    throw error;
  }
}

/**
 * 删除人设
 * @param personaId 人设ID
 * @param userId 用户ID (用于验证权限)
 */
export async function deletePersona(personaId: string, userId: string): Promise<void> {
  try {
    const command = new DeleteCommand({
      TableName: TABLE_NAME,
      Key: {
        userId, // 分区键
        personaId, // 排序键
      },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('删除人设失败', error);
    throw error;
  }
}
