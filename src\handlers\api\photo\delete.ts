import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { getPhotoById, deletePhotoById } from '@infrastructure/database/db/photo';
import { deleteObject } from '@shared/utils/s3';

// 定义验证规则
const deleteValidationSchema = {
  photoId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
};

/**
 * 删除照片接口
 * 支持删除照片记录和S3文件
 */
const deleteHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('PHOTO_DELETE_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(deleteValidationSchema);
    const { photoId } = validator(event);

    // 获取照片信息
    const photo = await getPhotoById(photoId);
    if (!photo) {
      return ResponseWrapper.error(ErrorCode.PHOTO_NOT_FOUND, '照片不存在', undefined, requestId);
    }

    // 验证用户权限
    if (photo.userId !== userId) {
      return ResponseWrapper.error(
        ErrorCode.ACCESS_DENIED,
        '无权限删除此照片',
        undefined,
        requestId,
      );
    }

    log.businessEvent('PHOTO_DELETE_PROCESSING', userId, {
      requestId,
      photoId,
      fileName: photo.fileName,
      s3Key: photo.s3Key,
    });

    // 删除S3文件
    try {
      const bucket = process.env.XHS_SOURCE_BUCKET_NAME!;
      await deleteObject(bucket, photo.s3Key);
      log.info('S3 file deleted successfully', {
        userId,
        photoId,
        s3Key: photo.s3Key,
        requestId,
      });
    } catch (s3Error) {
      // S3删除失败不应该阻止数据库记录的删除
      log.warn('Failed to delete S3 file, but continuing with database deletion', {
        userId,
        photoId,
        s3Key: photo.s3Key,
        error: s3Error,
        requestId,
      });
    }

    // 删除数据库记录
    await deletePhotoById(photoId);

    log.businessEvent('PHOTO_DELETE_SUCCESS', userId, {
      requestId,
      photoId,
      fileName: photo.fileName,
    });

    return ResponseWrapper.success(
      {
        message: '照片删除成功',
        photoId,
        fileName: photo.fileName,
      },
      requestId,
    );
  } catch (error) {
    log.error('Photo deletion failed', error as Error, {
      userId,
      requestId,
      errorName: (error as Error).name,
      errorMessage: (error as Error).message,
    });

    return ResponseWrapper.error(
      ErrorCode.INTERNAL_ERROR,
      `删除照片失败: ${error instanceof Error ? error.message : '未知错误'}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(deleteHandler);
