import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';

import { getMultipartUploadId } from '@shared/utils/s3';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { validateRequest, CommonRules } from '@shared/middleware/validation';
import { globalErrorHandler, handleExternalServiceError } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';

// 定义上传开始验证规则
const startUploadValidationSchema = {
  fileName: CommonRules.fileName,
  contentType: {
    required: true,
    type: 'string' as const,
    pattern: /^(application\/pdf|image\/(jpeg|jpg|png|gif|webp|svg\+xml|bmp|tiff))$/,
  },
};

const startUploadHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);
  let userId = '';

  try {
    // 身份验证
    userId = checkAuth(event);

    // 输入验证
    const validator = validateRequest(startUploadValidationSchema);
    const validatedData = validator(event);
    const { fileName, contentType } = validatedData;
    log.businessEvent('UPLOAD_START_REQUESTED', userId, {
      fileName,
      contentType,
      requestId,
    });

    // 获取多部分上传ID
    const { uploadId, fileKey } = await getMultipartUploadId(
      process.env.XHS_SOURCE_BUCKET_NAME!,
      fileName,
      contentType,
      userId,
    );

    log.businessEvent('UPLOAD_START_COMPLETED', userId, {
      fileName,
      fileKey,
      uploadId,
      requestId,
    });

    return ResponseWrapper.success(
      {
        uploadId,
        fileKey,
        fileName,
        contentType,
      },
      requestId,
    );
  } catch (error) {
    log.error('Upload start failed', error as Error, {
      userId,
      requestId,
    });

    return handleExternalServiceError(error, 'S3');
  }
};

export const handler: Handler = globalErrorHandler(startUploadHandler);
