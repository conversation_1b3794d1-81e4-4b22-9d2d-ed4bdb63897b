import 'dotenv/config';
import { getAIClient } from '@shared/utils/azureAi';
import { converseWithBedrock, createImageMessage } from '@shared/utils/bedrock';
import { getObjectBuffer } from '@shared/utils/s3';
import { BEDROCK_CLAUDE_MODEL_ID } from '@shared/utils/const';
import { getSettingPrompt } from '@src/infrastructure/database/db/setting';

/**
 * AI 照片分析服务
 * 支持多种 AI 服务提供商
 */

interface PhotoAnalysisResult {
  summary: string;
  tags?: string[];
  confidence?: number;
}

/**
 * 使用 Azure OpenAI GPT-4 Vision 分析照片
 * @param imageUrl 图片URL（S3预签名URL）
 * @returns 分析结果
 */
export async function analyzePhotoWithOpenAI(
  imageUrl: string,
  lang: string = 'en',
): Promise<PhotoAnalysisResult> {
  try {
    // 使用 Azure OpenAI 客户端，参考 llm.ts 的模式
    const client = getAIClient({
      apiKey: process.env.AZURE_AI_41_KEY!,
      apiVersion: '2024-12-01-preview',
      endpoint: process.env.AZURE_AI_41_ENDPOINT!,
    });
    const { photoSummary } = await getSettingPrompt();
    const prompt = photoSummary.replace('{{lang}}', lang);

    const response = await client.chat.completions.create({
      model: 'gpt-4o', // 使用 GPT-4o 模型，支持视觉功能
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: prompt,
            },
            {
              type: 'image_url',
              image_url: {
                url: imageUrl,
                detail: 'low', // 使用低分辨率以节省成本
              },
            },
          ],
        },
      ],
      max_tokens: 150,
      temperature: 0.3,
    });

    const summary = response.choices?.[0]?.message?.content?.trim() || '无法生成描述';

    return {
      summary,
      confidence: 0.9,
    };
  } catch (error) {
    console.error('Azure OpenAI 照片分析失败:', error);
    throw error;
  }
}

/**
 * 使用 AWS Bedrock Claude 3 分析照片
 * @param s3Key S3对象键
 * @returns 分析结果
 */
export async function analyzePhotoWithBedrock(
  s3Key: string,
  lang: string = 'en',
): Promise<PhotoAnalysisResult> {
  try {
    // 使用 s3.ts 中的方法获取图片数据
    const bucket = process.env.XHS_SOURCE_BUCKET_NAME!;
    const imageBuffer = await getObjectBuffer(bucket, s3Key);

    // 从文件扩展名检测图片格式
    const fileExtension = s3Key.split('.').pop()?.toLowerCase() || 'jpg';
    let imageFormat: 'png' | 'jpeg' | 'gif' | 'webp' = 'jpeg';

    if (fileExtension === 'png') {
      imageFormat = 'png';
    } else if (fileExtension === 'gif') {
      imageFormat = 'gif';
    } else if (fileExtension === 'webp') {
      imageFormat = 'webp';
    }
    const { photoSummary } = await getSettingPrompt();
    const prompt = photoSummary.replace('{{lang}}', lang);
    // 使用 bedrock.ts 中的通用方法
    const message = createImageMessage(prompt, imageBuffer, imageFormat);

    const summary = await converseWithBedrock(BEDROCK_CLAUDE_MODEL_ID, [message], {
      maxTokens: 150,
      temperature: 0.3,
    });

    return {
      summary: summary?.trim() || '无法生成描述',
      confidence: 0.9,
    };
  } catch (error) {
    console.error('AWS Bedrock 照片分析失败:', error);
    throw error;
  }
}

/**
 * 生成照片摘要的主函数
 * @param imageUrl 图片URL（用于 OpenAI）
 * @param s3Key S3对象键（用于 Bedrock）
 * @param provider AI服务提供商 ('openai' | 'bedrock')
 * @returns 照片摘要
 */
export async function generatePhotoSummary(
  imageUrl: string,
  s3Key: string,
  provider: 'openai' | 'bedrock' = 'bedrock',
  lang: string = 'en',
): Promise<string> {
  try {
    let result: PhotoAnalysisResult;

    switch (provider) {
      case 'openai':
        result = await analyzePhotoWithOpenAI(imageUrl, lang);
        break;
      case 'bedrock':
        result = await analyzePhotoWithBedrock(s3Key, lang);
        break;
      default:
        throw new Error(`不支持的 AI 服务提供商: ${provider}`);
    }

    return result.summary;
  } catch (error) {
    console.error('生成照片摘要失败:', error);
    // 返回默认描述而不是抛出错误，确保上传流程不会中断
    return '照片上传成功，摘要生成中...';
  }
}

/**
 * 批量生成照片摘要
 * @param photos 照片信息数组
 * @param provider AI服务提供商
 * @returns 处理结果
 */
export async function batchGeneratePhotoSummaries(
  photos: Array<{ id: string; s3Key: string; imageUrl: string }>,
  provider: 'openai' | 'bedrock' = 'bedrock',
): Promise<Array<{ id: string; summary: string; success: boolean }>> {
  const results = await Promise.allSettled(
    photos.map(async (photo) => {
      const summary = await generatePhotoSummary(photo.imageUrl, photo.s3Key, provider);
      return { id: photo.id, summary, success: true };
    }),
  );

  return results.map((result, index) => {
    if (result.status === 'fulfilled') {
      return result.value;
    }
    return {
      id: photos[index].id,
      summary: '摘要生成失败',
      success: false,
    };
  });
}
