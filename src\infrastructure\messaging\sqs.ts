import 'dotenv/config';

import { DeleteMessageCommandInput, SendMessageCommandInput, SQS } from '@aws-sdk/client-sqs';

const sqs = new SQS({
  region: process.env.XHS_AWS_REGION || 'ap-southeast-1',
});

export async function sendMessage(message: string, sqsUrl: string) {
  const params: SendMessageCommandInput = {
    MessageBody: message,
    QueueUrl: sqsUrl,
  };
  console.info(`send message: ${message} to ${sqsUrl}`);
  await new Promise((resolver, reject) => {
    sqs.sendMessage(params, (err, data) => {
      if (err) {
        console.error(err);
        return reject(err);
      }
      return resolver(data);
    });
  });
}

export async function sendFifoMessage(
  message: string,
  sqsUrl: string,
  groupId: string,
  deduplicationId: string,
) {
  const params: SendMessageCommandInput = {
    MessageBody: message,
    MessageDeduplicationId: deduplicationId, // Required for FIFO queues
    MessageGroupId: groupId, // Required for FIFO queues
    QueueUrl: sqsUrl,
  };
  console.info(`send message: ${message} to ${sqsUrl}`);
  await new Promise((resolver, reject) => {
    sqs.sendMessage(params, (err, data) => {
      if (err) {
        console.error(err);
        return reject(err);
      }
      return resolver(data);
    });
  });
}

export const deleteMessage = async (sqsName: string, receiptHandle: string) => {
  const params: DeleteMessageCommandInput = {
    QueueUrl: sqsName,
    ReceiptHandle: receiptHandle,
  };
  await new Promise((resolver, reject) => {
    sqs.deleteMessage(params, (err) => {
      if (err) {
        console.error(err);
        return reject(err);
      }
      return resolver('done');
    });
  });
};
