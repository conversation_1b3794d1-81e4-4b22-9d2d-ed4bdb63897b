import {
  PutCommand,
  QueryCommand,
  GetCommand,
  UpdateCommand,
  DeleteCommand,
} from '@aws-sdk/lib-dynamodb';

import { ISource } from '@core/types/interface';
import { getDBClient } from './init';

const dbClient = getDBClient();
const TABLE_NAME = 'XHS_INFO_SOURCE';

/**
 * 创建信息源
 * @param data 信息源数据
 */
export async function createSource(data: ISource) {
  try {
    const command = new PutCommand({
      TableName: TABLE_NAME,
      Item: {
        ...data,
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt || new Date().toISOString(),
      },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('创建信息源失败', error);
    throw error;
  }
}

/**
 * 根据用户ID获取信息源列表
 * @param userId 用户ID
 * @returns 信息源列表
 */
export async function getSourcesByUserId(userId: string): Promise<ISource[]> {
  try {
    let items: ISource[] = [];
    let lastEvaluatedKey: any;

    do {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        IndexName: 'userId-index', // 使用GSI查询
        KeyConditionExpression: 'userId = :userId',
        ExpressionAttributeValues: {
          ':userId': userId,
        },
        ExclusiveStartKey: lastEvaluatedKey,
        ScanIndexForward: false, // 按创建时间倒序
      });

      // eslint-disable-next-line no-await-in-loop
      const result = await dbClient.send(command);

      if (result.Items) {
        items = items.concat(result.Items as ISource[]);
      }

      lastEvaluatedKey = result.LastEvaluatedKey;
    } while (lastEvaluatedKey);

    // 按创建时间倒序排列
    return items.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  } catch (error) {
    console.error('获取信息源列表失败', error);
    throw error;
  }
}

/**
 * 根据用户ID和人设ID获取信息源列表
 * @param userId 用户ID
 * @param personaId 人设ID
 * @returns 信息源列表
 */
export async function getSourcesByUserAndPersona(
  userId: string,
  personaId: string,
): Promise<ISource[]> {
  try {
    let items: ISource[] = [];
    let lastEvaluatedKey: any;

    do {
      const command = new QueryCommand({
        TableName: TABLE_NAME,
        IndexName: 'userId-index', // 使用GSI查询
        KeyConditionExpression: 'userId = :userId',
        FilterExpression: 'personaId = :personaId',
        ExpressionAttributeValues: {
          ':userId': userId,
          ':personaId': personaId,
        },
        ExclusiveStartKey: lastEvaluatedKey,
        ScanIndexForward: false, // 按创建时间倒序
      });

      // eslint-disable-next-line no-await-in-loop
      const result = await dbClient.send(command);

      if (result.Items) {
        items = items.concat(result.Items as ISource[]);
      }

      lastEvaluatedKey = result.LastEvaluatedKey;
    } while (lastEvaluatedKey);

    // 按创建时间倒序排列
    return items.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  } catch (error) {
    console.error('获取信息源列表失败', error);
    throw error;
  }
}

/**
 * 获取单个信息源信息
 * @param sourceId 信息源ID
 * @param userId 用户ID
 * @returns 信息源信息
 */
export async function getSourceById(sourceId: string): Promise<ISource | null> {
  try {
    const command = new GetCommand({
      TableName: TABLE_NAME,
      Key: {
        id: sourceId, // 排序键
      },
    });
    const result = await dbClient.send(command);
    return (result.Item as ISource) || null;
  } catch (error) {
    console.error('获取信息源信息失败', error);
    throw error;
  }
}

/**
 * 更新信息源连接状态
 * @param sourceId 信息源ID
 * @param isConnection 连接状态
 */
export async function updateSourceConnectionStatus(
  sourceId: string,
  isConnection: boolean,
): Promise<void> {
  try {
    const command = new UpdateCommand({
      TableName: TABLE_NAME,
      Key: {
        id: sourceId,
      },
      UpdateExpression: 'SET isConnection = :isConnection, updatedAt = :updatedAt',
      ExpressionAttributeValues: {
        ':isConnection': isConnection,
        ':updatedAt': new Date().toISOString(),
      },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('更新信息源连接状态失败', error);
    throw error;
  }
}

/**
 * 删除信息源
 * @param sourceId 信息源ID
 */
export async function deleteSource(sourceId: string): Promise<void> {
  try {
    const command = new DeleteCommand({
      TableName: TABLE_NAME,
      Key: {
        id: sourceId, // 主键
      },
    });
    await dbClient.send(command);
  } catch (error) {
    console.error('删除信息源失败', error);
    throw error;
  }
}
