import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { getGenerationTask } from '@infrastructure/database/db/generationTasks';
import { TaskStatus, TaskResultResponse } from '@core/types/generationTask';
import { getPhotoById } from '@src/infrastructure/database/db/photo';
import { genReadSignedUrl } from '@src/shared/utils/s3';

/**
 * 获取任务结果API
 */
const resultHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);
  const userId = checkAuth(event);

  try {
    // 验证请求体
    if (!event.body) {
      return ResponseWrapper.error(
        ErrorCode.VALIDATION_ERROR,
        '请求体不能为空',
        undefined,
        requestId,
      );
    }

    let requestBody;
    try {
      requestBody = JSON.parse(event.body);
    } catch (parseError) {
      return ResponseWrapper.error(
        ErrorCode.VALIDATION_ERROR,
        '请求体格式错误，必须是有效的JSON',
        undefined,
        requestId,
      );
    }

    // 从body获取taskId
    const { taskId } = requestBody;
    if (!taskId || typeof taskId !== 'string') {
      return ResponseWrapper.error(
        ErrorCode.VALIDATION_ERROR,
        '缺少有效的任务ID',
        undefined,
        requestId,
      );
    }

    log.businessEvent('TASK_RESULT_QUERY_STARTED', userId, {
      requestId,
      taskId,
    });

    // 获取任务详情
    const task = await getGenerationTask(taskId);

    if (!task) {
      return ResponseWrapper.error(ErrorCode.VALIDATION_ERROR, '任务不存在', undefined, requestId);
    }

    // 验证任务所有权
    if (task.userId !== userId) {
      log.securityEvent('UNAUTHORIZED_TASK_ACCESS', userId, 'high', {
        taskId,
        requestId,
      });
      return ResponseWrapper.error(ErrorCode.ACCESS_DENIED, '无权访问此任务', undefined, requestId);
    }

    // 检查任务是否完成
    if (task.status !== TaskStatus.COMPLETED) {
      return ResponseWrapper.error(
        ErrorCode.VALIDATION_ERROR,
        `任务尚未完成，当前状态: ${task.status}`,
        task.stage,
        requestId,
      );
    }

    // 验证任务数据完整性
    if (!task.finalContent) {
      return ResponseWrapper.error(
        ErrorCode.VALIDATION_ERROR,
        '任务已完成但缺少最终内容',
        undefined,
        requestId,
      );
    }
    // 获取到photos，将Id转为真实的路径
    if (task.finalContent && task.finalContent.length > 0) {
      task.finalContent = await Promise.all(
        task.finalContent.map(async (t: any) => {
          const photos = await Promise.all(
            t.photos.map(async (photoId: string) => {
              const photo: any = await getPhotoById(photoId);
              const imageUrl = await genReadSignedUrl(
                process.env.XHS_SOURCE_BUCKET_NAME!,
                photo.s3Key,
                3600,
              );
              return {
                id: photoId,
                imageUrl,
              };
            }),
          );
          return {
            ...t,
            photos,
          };
        }),
      );
    }

    // 构建结果响应
    const result: TaskResultResponse = {
      taskId,
      finalContent: task.finalContent || [],
      candidateContent: task.candidateContent || [],
      metadata: {
        usedPhotos: task.selectedPhotos || [],
        keywords: task.searchKeywords || [],
        sources: task.topNResults || [],
      },
    };

    log.businessEvent('TASK_RESULT_QUERY_SUCCESS', userId, {
      requestId,
      taskId,
      contentLength: task.finalContent?.length || 0,
    });

    return ResponseWrapper.success(result, requestId);
  } catch (error) {
    log.error('Query task result failed', error as Error, {
      userId,
      requestId,
      errorName: (error as Error).name,
      errorMessage: (error as Error).message,
    });

    // 如果是认证错误，返回401
    if (error instanceof Error && error.message === 'Unauthorized') {
      return ResponseWrapper.error(ErrorCode.UNAUTHORIZED, '用户未认证', undefined, requestId);
    }

    // 如果是数据库相关错误，提供更具体的错误信息
    if (error instanceof Error && error.message.includes('DynamoDB')) {
      return ResponseWrapper.error(
        ErrorCode.DATABASE_ERROR,
        '数据库查询失败',
        undefined,
        requestId,
      );
    }

    return ResponseWrapper.error(
      ErrorCode.INTERNAL_ERROR,
      `查询结果失败: ${(error as Error).message}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(resultHandler);
