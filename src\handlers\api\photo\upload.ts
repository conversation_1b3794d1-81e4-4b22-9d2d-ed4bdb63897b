import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth, getLanguage } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { batchCreatePhotos } from '@lib/photo';
import { sendMessage } from '@infrastructure/messaging/sqs';

// 定义验证规则
const uploadValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
  },
};

const uploadHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);
  const lang = getLanguage(event);

  try {
    log.businessEvent('PHOTO_UPLOAD_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(uploadValidationSchema);
    const { personaId } = validator(event);

    // 手动验证 photos 数组
    const body = JSON.parse(event.body || '{}');
    const { photos } = body;

    if (!Array.isArray(photos) || photos.length === 0) {
      return ResponseWrapper.error(
        ErrorCode.VALIDATION_ERROR,
        '照片数组不能为空',
        undefined,
        requestId,
      );
    }

    if (photos.length > 50) {
      return ResponseWrapper.error(
        ErrorCode.VALIDATION_ERROR,
        '一次最多只能上传50张照片',
        undefined,
        requestId,
      );
    }

    // 验证每个照片对象
    const invalidPhotos = photos.filter(
      (photo: any) =>
        !photo ||
        typeof photo !== 'object' ||
        !photo.fileName ||
        !photo.s3Key ||
        typeof photo.fileName !== 'string' ||
        typeof photo.s3Key !== 'string' ||
        photo.fileName.length === 0 ||
        photo.s3Key.length === 0,
    );

    if (invalidPhotos.length > 0) {
      return ResponseWrapper.error(
        ErrorCode.VALIDATION_ERROR,
        '照片数据格式不正确，每个照片必须包含 fileName 和 s3Key 字段',
        undefined,
        requestId,
      );
    }

    log.businessEvent('PHOTO_UPLOAD_PROCESSING', userId, {
      requestId,
      personaId,
      photoCount: photos.length,
    });

    // 批量创建照片记录
    const result = await batchCreatePhotos(userId, personaId, photos);

    // 为成功创建的照片发送摘要生成任务
    const successfulPhotos = result.details.filter((detail) => detail.status === 'created');
    if (successfulPhotos.length > 0) {
      try {
        const queueUrl = process.env.PHOTO_SUMMARY_QUEUE_URL;
        if (queueUrl) {
          // 为每个成功创建的照片发送摘要生成任务
          const summaryTasks = successfulPhotos.map(async (detail) => {
            if (detail.photoId && detail.s3Key) {
              const task = {
                photoId: detail.photoId,
                s3Key: detail.s3Key,
                userId,
                fileName: detail.fileName,
                lang,
              };
              await sendMessage(JSON.stringify(task), queueUrl);
            }
          });

          await Promise.allSettled(summaryTasks);

          log.businessEvent('PHOTO_SUMMARY_TASKS_QUEUED', userId, {
            requestId,
            taskCount: successfulPhotos.length,
          });
        }
      } catch (error) {
        // 摘要生成任务失败不应该影响主流程
        log.error('Failed to queue photo summary tasks', error as Error, {
          userId,
          requestId,
        });
      }
    }

    log.businessEvent('PHOTO_UPLOAD_COMPLETED', userId, {
      requestId,
      personaId,
      created: result.created,
      skipped: result.skipped,
      errors: result.errors,
    });

    return ResponseWrapper.success(
      {
        message: '照片上传处理完成',
        summary: {
          total: photos.length,
          created: result.created,
          skipped: result.skipped,
          errors: result.errors,
        },
        details: result.details,
      },
      requestId,
    );
  } catch (error) {
    log.error('Photo upload failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.PHOTO_UPLOAD_FAILED,
      `照片上传失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(uploadHandler);
