import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import {
  updateGenerateList,
  getGenerateListById,
  createGenerateList,
} from '@infrastructure/database/db/generateList';
import {
  getGenerationTask,
  updateGenerationTask,
} from '@infrastructure/database/db/generationTasks';
import { updatePhotoUsage } from '@src/infrastructure/database/db/photo';
import { GenerateListStatus } from './generateList';

// 定义生成列表状态枚举
// enum GenerateListStatus {
//   INIT = 'INIT', // 初始化状态，未发布或未收藏
//   FAVORITES = 'FAVORITES',
//   PUBLISHED = 'PUBLISHED',
//   DELETED = 'DELETED',
// }
//   data: IGenerateList,
//   status: string,
//   taskId: string
// 定义更新验证规则
const updateValidationSchema = {
  taskId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
};

const updateHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('GENERATE_LIST_UPDATE_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(updateValidationSchema);
    const { taskId } = validator(event);
    const { data, status, personaId } = JSON.parse(event.body || '{}');

    // 检查生成列表是否存在
    const existingGenerateList = await getGenerateListById(data.id);

    // 检查status === PUBLISHED
    // 没有的话创建
    if (!existingGenerateList) {
      log.businessEvent('GENERATE_LIST_NOT_FOUND', userId, {
        requestId,
        generateListId: data.id,
      });
      const query = {
        ...data,
        taskId,
        status,
        userId,
        personaId,
        updatedAt: new Date().toISOString(),
      };
      await createGenerateList(query);
    }
    // 有的话更新

    // 执行状态更新
    await updateGenerateList(data.id, { status });

    // 同时更新XHS_GENERATION_TASKS表中finalContent里对应项目的status
    try {
      const generationTask = await getGenerationTask(taskId);
      if (generationTask && generationTask.finalContent && generationTask.finalContent.length > 0) {
        // 更新finalContent数组中匹配的项目状态
        const updatedFinalContent = generationTask.finalContent.map((item: any) => {
          // 根据id匹配对应的内容项
          if (item.id === data.id) {
            return {
              ...item,
              status,
              updatedAt: new Date().toISOString(),
            };
          }
          return item;
        });

        // 更新任务的finalContent
        await updateGenerationTask(taskId, {
          finalContent: updatedFinalContent,
        });

        log.businessEvent('GENERATION_TASK_FINAL_CONTENT_STATUS_UPDATED', userId, {
          requestId,
          taskId,
          generateListId: data.id,
          newStatus: status,
        });
      }
    } catch (taskUpdateError) {
      // 记录错误但不影响主流程
      log.error('Failed to update generation task finalContent status', taskUpdateError as Error, {
        userId,
        requestId,
        taskId,
        generateListId: data.id,
      });
    }

    // 更新照片为已使用
    if (status === GenerateListStatus.PUBLISHED) {
      await Promise.all(
        data.photos.map(async (photo: any) => {
          await updatePhotoUsage(photo.id, true);
        }),
      );
    }
    return ResponseWrapper.success(
      {
        message: '生成列表状态更新成功',
        id: data.id,
        status,
      },
      requestId,
    );
  } catch (error) {
    log.error('Generate list update failed', error as Error, {
      userId,
      requestId,
      errorName: (error as Error).name,
      errorMessage: (error as Error).message,
    });

    return ResponseWrapper.error(
      ErrorCode.INTERNAL_ERROR,
      `更新生成列表失败: ${error instanceof Error ? error.message : '未知错误'}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(updateHandler);
