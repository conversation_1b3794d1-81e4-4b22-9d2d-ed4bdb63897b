import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { createSource } from '@lib/source';
import { v4 as uuidv4 } from 'uuid';

// 定义创建信息源验证规则
const createSourceValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
  type: {
    required: true,
    type: 'string' as const,
    enum: ['rss', 'url', 'account'],
  },
  name: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
  url: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 500,
  },
  isBuiltIn: {
    required: false,
    type: 'boolean' as const,
    default: false,
  },
  isDomestic: {
    required: false,
    type: 'boolean' as const,
    default: true,
  },
};

const createHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);
  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('SOURCE_CREATE_STARTED', userId, {
      requestId,
    });

    const validator = validateRequest(createSourceValidationSchema);
    const { personaId, type, name, url, isBuiltIn, isDomestic } = validator(event);

    const sourceId = uuidv4();
    const { contentType } = JSON.parse(event.body || '{}');

    // 处理contentType，如果是字符串则解析为数组
    let parsedContentType: string[] = [];
    if (contentType) {
      try {
        parsedContentType = typeof contentType === 'string' ? JSON.parse(contentType) : contentType;
      } catch {
        parsedContentType = [];
      }
    }

    const insertData = {
      id: sourceId,
      userId,
      personaId,
      type,
      name,
      url,
      isBuiltIn: isBuiltIn || false,
      isDomestic: isDomestic !== undefined ? isDomestic : true,
      isConnection: false, // 新创建的信息源默认未连接
      contentType: parsedContentType,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await createSource(insertData);

    log.businessEvent('SOURCE_CREATE_SUCCESS', userId, {
      requestId,
      sourceId,
      type,
      name,
    });

    return ResponseWrapper.success(
      {
        message: '信息源创建成功',
        source: insertData,
      },
      requestId,
    );
  } catch (error) {
    log.error('Source creation failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.SOURCE_CREATE_FAILED,
      `创建信息源失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(createHandler);
