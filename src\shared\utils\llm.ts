// import logger from './logger';

import { getMiniClient, getAIClient } from './azureAi';

export async function aiChatString(messages: any[]): Promise<string> {
  try {
    const client = getMiniClient(false);
    const response = await client.chat.completions.create({
      model: 'gpt-4o-mini',
      messages,
      max_tokens: 16384,
      temperature: 0.7,
    });
    return response.choices[0].message?.content || '';
  } catch (error) {
    console.error('Error in AI chat:', error);
    return '';
  }
}

/**
 * 使用 Azure OpenAI 进行对话
 * @param messages
 * @param options
 * @returns
 */
export async function aiChat(messages: any[]) {
  const client = getAIClient({
    apiKey: process.env.AZURE_AI_41_KEY!,
    apiVersion: '2024-12-01-preview',
    endpoint: process.env.AZURE_AI_41_ENDPOINT!,
  });
  try {
    const response = await client.chat.completions.create({
      model: 'gpt-4.1',
      messages,
      max_tokens: 16384,
      temperature: 0.7,
      response_format: { type: 'json_object' },
    });

    return response.choices[0].message?.content || '';
  } catch (error) {
    console.error('Error in AI chat:', error);
    return '';
  }
}
