import 'dotenv/config';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';

let docClient: DynamoDBDocumentClient;

export function getDBClient() {
  if (docClient) {
    return docClient;
  }
  const client = new DynamoDBClient({
    region: process.env.XHS_AWS_REGION,
  });
  docClient = DynamoDBDocumentClient.from(client);
  return docClient;
}
