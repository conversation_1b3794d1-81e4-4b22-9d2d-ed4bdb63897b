import { ErrorCode, ErrorMessages } from '../types/response';

/**
 * 自定义错误类
 */
export class ApiError extends Error {
  constructor(
    public errorCode: ErrorCode,
    message?: string,
    public details?: any,
  ) {
    super(message || ErrorMessages[errorCode]);
    this.name = 'ApiError';
  }
}

export class ValidationError extends Error {
  constructor(
    message: string,
    public details?: any,
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class UnauthorizedError extends Error {
  constructor(message: string = '未授权访问') {
    super(message);
    this.name = 'UnauthorizedError';
  }
}

export class NotFoundError extends Error {
  constructor(resource: string) {
    super(`${resource}未找到`);
    this.name = 'NotFoundError';
  }
}
