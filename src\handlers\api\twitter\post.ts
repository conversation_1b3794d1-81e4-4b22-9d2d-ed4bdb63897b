import { APIGatewayEvent, Hand<PERSON> } from 'aws-lambda';

import { getTwitterSession, updateTwitterSession } from '@lib/twitterSession';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { checkAuth } from '@shared/utils/auth';
import { getRequestId } from '@shared/utils/requestUtils';
import { ResponseWrapper } from '@shared/utils/response';
import { EncryptionService } from '@shared/utils/twitter/encryption';
import { TwitterManager } from '@shared/utils/twitter/manager';

interface PostTweetRequest {
  text: string;
  options?: {
    reply_to?: string;
    media_ids?: string[];
  };
}

const postTweetHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    // 解析请求体
    const body: PostTweetRequest = JSON.parse(event.body || '{}');
    const { text, options } = body;

    // 验证输入
    if (!text || text.trim().length === 0) {
      return ResponseWrapper.error(
        'VALIDATION_ERROR' as any,
        '推文内容不能为空',
        undefined,
        requestId,
      );
    }

    if (text.length > 280) {
      return ResponseWrapper.error(
        'VALIDATION_ERROR' as any,
        '推文内容不能超过280个字符',
        undefined,
        requestId,
      );
    }

    // 获取用户的Twitter session
    const session = await getTwitterSession(userId);

    if (!session) {
      return ResponseWrapper.error(
        'TWITTER_NOT_CONNECTED' as any,
        'Twitter账户未连接，请先进行授权',
        { requiresAuth: true },
        requestId,
      );
    }

    // 检查token是否过期
    if (new Date() > new Date(session.expiresAt)) {
      return ResponseWrapper.error(
        'TWITTER_TOKEN_EXPIRED' as any,
        'Twitter授权已过期，请重新授权',
        { requiresReauth: true },
        requestId,
      );
    }

    // 解密tokens
    const encryption = new EncryptionService();
    const accessToken = await encryption.decrypt(session.accessToken);
    const refreshToken = session.refreshToken
      ? await encryption.decrypt(session.refreshToken)
      : undefined;

    const tokens = {
      accessToken,
      refreshToken,
      expiresAt: new Date(session.expiresAt),
    };

    // 初始化Twitter管理器
    const twitterManager = new TwitterManager({
      clientId: process.env.TWITTER_CLIENT_ID!,
      clientSecret: process.env.TWITTER_CLIENT_SECRET!,
      redirectUri: process.env.TWITTER_REDIRECT_URI!,
      scopes: ['tweet.read', 'tweet.write', 'users.read', 'offline.access'],
    });

    // 构建推文数据
    const tweetData: any = { text: text.trim() };
    if (options?.reply_to) {
      tweetData.reply = { in_reply_to_tweet_id: options.reply_to };
    }
    if (options?.media_ids) {
      tweetData.media_ids = options.media_ids;
    }

    // 发送推文
    const result = await twitterManager.postTweet(tokens, tweetData);

    if (result.success) {
      return ResponseWrapper.success(
        {
          tweetId: result.tweetId,
          text: text.trim(),
          twitterUsername: session.twitterUsername,
          createdAt: new Date().toISOString(),
          rateLimitStatus: result.rateLimitStatus,
        },
        requestId,
      );
    }

    // 如果是token过期，尝试刷新
    if (result.error?.includes('401') && tokens.refreshToken) {
      try {
        const newTokens = await twitterManager.refreshUserTokens(tokens.refreshToken);

        // 更新数据库中的tokens
        await updateTwitterSession(userId, {
          accessToken: await encryption.encrypt(newTokens.accessToken),
          refreshToken: newTokens.refreshToken
            ? await encryption.encrypt(newTokens.refreshToken)
            : session.refreshToken,
          expiresAt: newTokens.expiresAt?.toISOString(),
        });

        // 重新尝试发送推文
        const retryResult = await twitterManager.postTweet(newTokens, tweetData);

        if (retryResult.success) {
          return ResponseWrapper.success(
            {
              tweetId: retryResult.tweetId,
              text: text.trim(),
              twitterUsername: session.twitterUsername,
              createdAt: new Date().toISOString(),
              rateLimitStatus: retryResult.rateLimitStatus,
            },
            requestId,
          );
        }
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
      }
    }

    return ResponseWrapper.error(
      'TWEET_FAILED' as any,
      '发送推文失败',
      {
        details: result.error,
        requiresReauth: result.error?.includes('401'),
      },
      requestId,
    );
  } catch (error) {
    console.error('Post tweet error:', error);
    return ResponseWrapper.error(
      'INTERNAL_ERROR' as any,
      '发送推文时发生内部错误',
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(postTweetHandler);
