import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { getPhotosByType } from '@lib/photo';
import { genReadSignedUrl } from '@shared/utils/s3';
/**
 * 获取类型描述
 * @param type 类型
 * @returns 类型描述
 */
function getTypeDescription(type: string): string {
  switch (type) {
    case 'total':
      return '所有照片';
    case 'used':
      return '已使用的照片';
    case 'unused':
      return '未使用的照片';
    default:
      return `AI分类: ${type}`;
  }
}
// 定义验证规则
const filterValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
  },
  type: {
    required: true,
    type: 'string' as const,
    minLength: 1,
  },
  limit: {
    required: false,
    type: 'number' as const,
    min: 1,
    max: 100,
  },
  lastEvaluatedKey: {
    required: false,
    type: 'string' as const,
  },
};

/**
 * 根据类型筛选照片接口
 * 支持按使用状态和AI分类类型筛选，支持分页
 */
const filterHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('PHOTO_FILTER_STARTED', userId, {
      requestId,
    });

    // 输入验证
    const validator = validateRequest(filterValidationSchema);
    const { personaId, type, limit = 50, lastEvaluatedKey } = validator(event);

    // 解析分页键
    let parsedLastEvaluatedKey;
    if (lastEvaluatedKey) {
      try {
        parsedLastEvaluatedKey = JSON.parse(lastEvaluatedKey);
      } catch (error) {
        return ResponseWrapper.error(
          ErrorCode.VALIDATION_ERROR,
          '分页键格式不正确',
          undefined,
          requestId,
        );
      }
    }

    log.businessEvent('PHOTO_FILTER_PROCESSING', userId, {
      requestId,
      personaId,
      type,
      limit,
    });

    // 获取筛选后的照片列表
    const result = await getPhotosByType(userId, personaId, type, limit, parsedLastEvaluatedKey);

    // 为照片生成预签名 URL
    const bucket = process.env.XHS_SOURCE_BUCKET_NAME!;
    const photosWithUrls = await Promise.all(
      result.photos.map(async (photo) => {
        try {
          // 生成1小时有效期的预签名URL
          const imageUrl = await genReadSignedUrl(bucket, photo.s3Key, 3600);
          return {
            ...photo,
            imageUrl,
          };
        } catch (error) {
          log.error('Failed to generate signed URL for photo', error as Error, {
            userId,
            photoId: photo.id,
            s3Key: photo.s3Key,
            requestId,
          });
          // 如果生成预签名URL失败，返回不带URL的照片信息
          return photo;
        }
      }),
    );

    log.businessEvent('PHOTO_FILTER_COMPLETED', userId, {
      requestId,
      personaId,
      type,
      photoCount: result.count,
      totalCount: result.totalCount,
      hasMore: !!result.lastEvaluatedKey,
    });

    return ResponseWrapper.success(
      {
        photos: photosWithUrls,
        pagination: {
          count: result.count,
          totalCount: result.totalCount,
          hasMore: !!result.lastEvaluatedKey,
          lastEvaluatedKey: result.lastEvaluatedKey
            ? JSON.stringify(result.lastEvaluatedKey)
            : undefined,
        },
        filters: {
          userId,
          personaId,
          type,
          limit,
        },
        metadata: {
          filterType: type,
          description: getTypeDescription(type),
          generatedAt: new Date().toISOString(),
        },
      },
      requestId,
    );
  } catch (error) {
    log.error('Photo filter failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.PHOTO_LIST_FAILED,
      `照片筛选失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(filterHandler);
