import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { getConfigsByUserId } from '@lib/config';

const listHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);
  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('CONFIG_LIST_STARTED', userId, {
      requestId,
    });

    // 获取用户的所有配置
    const configs = await getConfigsByUserId(userId);

    log.businessEvent('CONFIG_LIST_SUCCESS', userId, {
      requestId,
      count: configs.length,
    });

    return ResponseWrapper.success(
      {
        message: '获取配置列表成功',
        configs,
        total: configs.length,
      },
      requestId,
    );
  } catch (error) {
    log.error('Config list failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.INTERNAL_ERROR,
      `获取配置列表失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(listHandler);
