import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { getUserById } from '@lib/user';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { NotFoundError } from '@shared/utils/errors';
import { globalErrorHandler, handleDatabaseError } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';

const userHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);

  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('USER_INFO_REQUESTED', userId, { requestId });

    // 获取用户信息
    const user = await getUserById(userId);
    if (!user) {
      throw new NotFoundError('用户');
    }

    return ResponseWrapper.success(
      {
        user,
      },
      requestId,
    );
  } catch (error) {
    log.error('User info retrieval failed', error as Error, {
      userId,
      requestId,
    });

    if (error instanceof NotFoundError) {
      throw error;
    }

    return handleDatabaseError(error, 'getUserInfo');
  }
};

export const handler: Handler = globalErrorHandler(userHandler);
