import { AzureOpenAI as OpenAIClient } from 'openai';

let client: OpenAIClient | null = null;
let embeddingClient: OpenAIClient | null = null;
let miniClient: OpenAIClient | null = null;

interface AzureAIConfig {
  apiKey: string;
  apiVersion: string;
  endpoint: string;
}
/**
 * 验证配置并返回完整配置对象
 */
function validateConfig(config: Partial<AzureAIConfig>): AzureAIConfig {
  if (!config.apiKey || !config.endpoint) {
    throw new Error('Missing required Azure AI configuration');
  }
  return {
    apiKey: config.apiKey,
    apiVersion: config.apiVersion || '2024-08-01-preview',
    endpoint: config.endpoint,
  };
}
/**
 * 获取迷你模型客户端
 */
export function getMiniClient(forceNew = false): OpenAIClient {
  if (forceNew || !miniClient) {
    const config = validateConfig({
      apiKey: process.env.AZURE_MINI_AI_KEY,
      apiVersion: '2024-08-01-preview',
      endpoint: process.env.AZURE_MINI_AI_ENDPOINT,
    });

    miniClient = new OpenAIClient({
      apiKey: config.apiKey,
      endpoint: config.endpoint,
      apiVersion: config.apiVersion,
    });
  }
  return miniClient;
}

/**
 * 获取标准模型客户端
 */
export function getClient(forceNew = false): OpenAIClient {
  if (forceNew || !client) {
    const config = validateConfig({
      apiKey: process.env.AZURE_AI_KEY,
      apiVersion: '2024-08-01-preview',
      endpoint: process.env.AZURE_AI_ENDPOINT,
    });

    client = new OpenAIClient({
      apiKey: config.apiKey,
      endpoint: config.endpoint,
      apiVersion: config.apiVersion,
    });
  }
  return client;
}

/**
 * 获取嵌入模型客户端
 */
export function getEmbeddingClient(forceNew = false): OpenAIClient {
  if (forceNew || !embeddingClient) {
    const config = validateConfig({
      apiKey: process.env.AZURE_EMBEDDING_AI_KEY,
      apiVersion: '2024-08-01-preview',
      endpoint: process.env.AZURE_EMBEDDING_AI_ENDPOINT,
    });

    embeddingClient = new OpenAIClient({
      apiKey: config.apiKey,
      endpoint: config.endpoint,
      apiVersion: config.apiVersion,
    });
  }
  return embeddingClient;
}

export interface AzureAIClientConfig {
  apiKey: string;
  apiVersion: string;
  endpoint: string;
}
export const getAIClient = ({ apiKey, apiVersion, endpoint }: AzureAIClientConfig) => {
  if (!client) {
    const config = validateConfig({ apiKey, apiVersion, endpoint });
    client = new OpenAIClient({
      apiKey: config.apiKey,
      endpoint: config.endpoint,
      apiVersion: config.apiVersion,
    });
  }
  return client;
};
