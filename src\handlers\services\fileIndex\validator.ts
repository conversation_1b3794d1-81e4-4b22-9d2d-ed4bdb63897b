import { getFileById } from '@lib/file';
import { StatusEnum } from '@constants/enum';
import { log } from '@shared/utils/structuredLogger';
import { FileIndexPayload } from './types';
import { InvalidPayloadError, FileNotFoundError } from './error/errors';

/**
 * 验证SQS消息载荷
 * @param payloadStr JSON字符串载荷
 * @returns 解析后的载荷对象
 * @throws InvalidPayloadError
 */
export function validatePayload(payloadStr: string): FileIndexPayload {
  let payload: FileIndexPayload;

  try {
    payload = JSON.parse(payloadStr);
  } catch (error) {
    throw new InvalidPayloadError('Invalid JSON format');
  }

  const { fileId, userId, personaId, s3Key, fileName } = payload;

  // 检查必需字段
  const missingFields: string[] = [];
  if (!fileId) missingFields.push('fileId');
  if (!userId) missingFields.push('userId');
  if (!personaId) missingFields.push('personaId');
  if (!s3Key) missingFields.push('s3Key');
  if (!fileName) missingFields.push('fileName');

  if (missingFields.length > 0) {
    throw new InvalidPayloadError(`Missing required fields: ${missingFields.join(', ')}`);
  }

  // 基本格式验证
  if (typeof fileId !== 'string' || fileId.trim().length === 0) {
    throw new InvalidPayloadError('fileId must be a non-empty string');
  }

  if (typeof userId !== 'string' || userId.trim().length === 0) {
    throw new InvalidPayloadError('userId must be a non-empty string');
  }

  if (typeof s3Key !== 'string' || s3Key.trim().length === 0) {
    throw new InvalidPayloadError('s3Key must be a non-empty string');
  }

  return payload;
}

/**
 * 检查文件是否存在并验证处理状态
 * @param fileId 文件ID
 * @param userId 用户ID
 * @returns 文件对象
 * @throws FileNotFoundError
 */
export async function validateFileAndStatus(fileId: string) {
  const file = await getFileById(fileId);

  if (!file) {
    throw new FileNotFoundError(fileId);
  }

  return file;
}

/**
 * 检查文件是否需要处理
 * @param file 文件对象
 * @param userId 用户ID
 * @returns 是否需要处理
 */
export function shouldProcessFile(file: any, userId: string): boolean {
  const { embeddingStatus } = file;
  // 如果已经在处理中或已完成，则跳过
  if (embeddingStatus === StatusEnum.PARSING || embeddingStatus === StatusEnum.SUCCESS) {
    log.businessEvent('FILE_INDEX_SKIPPED', userId, {
      fileId: file.id,
      fileName: file.fileName,
      status: embeddingStatus,
    });
    return false;
  }

  return true;
}

/**
 * 验证环境变量配置
 * @throws Error
 */
export function validateEnvironment(): void {
  const requiredEnvVars = [
    'FILE_INDEX_QUEUE',
    'FILE_PAGE_IMG_QUEUE',
    'XHS_SOURCE_BUCKET_NAME',
    'AZURE_DIC_ENDPOINT',
    'AZURE_DIC_KEY',
  ];

  const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }
}

/**
 * 验证处理配置
 * @param config 处理配置
 * @throws Error
 */
export function validateProcessingConfig(config: any): void {
  const { minTextLength, embeddingBatchSize, embeddingParallelCount } = config;

  if (typeof minTextLength !== 'number' || minTextLength < 0) {
    throw new Error('minTextLength must be a non-negative number');
  }

  if (typeof embeddingBatchSize !== 'number' || embeddingBatchSize < 1) {
    throw new Error('embeddingBatchSize must be a positive number');
  }

  if (typeof embeddingParallelCount !== 'number' || embeddingParallelCount < 1) {
    throw new Error('embeddingParallelCount must be a positive number');
  }
}
