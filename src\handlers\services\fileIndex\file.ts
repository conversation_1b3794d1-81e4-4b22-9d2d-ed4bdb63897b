import 'dotenv/config';
import { genReadSignedUrl } from '@shared/utils/s3';
import { createHash } from 'crypto';

import DocumentIntelligence, {
  AnalyzeOperationOutput,
  getLongRunningPoller,
  isUnexpected,
} from '@azure-rest/ai-document-intelligence';

interface OcrDoc {
  page_content: string;
  metadata: {
    page: number;
  };
}

/**
 * OCR PDF 文档
 * @param fileKey PDF 文件的 key
 * @returns OCR 结果
 */
export async function ocrPdf(fileKey: string): Promise<OcrDoc[]> {
  const url = await genReadSignedUrl(process.env.XHS_SOURCE_BUCKET_NAME!, fileKey, 300);
  const client = DocumentIntelligence(process.env.AZURE_DIC_ENDPOINT!, {
    key: process.env.AZURE_DIC_KEY!,
  });
  const initialResponse = await client
    .path('/documentModels/{modelId}:analyze', 'prebuilt-layout')
    .post({
      contentType: 'application/json',
      body: {
        urlSource: url,
      },
    });
  if (isUnexpected(initialResponse)) {
    throw new Error(String(initialResponse.body?.error) || 'unknown error');
  }
  const poller = getLongRunningPoller(client, initialResponse);
  const result = (await poller.pollUntilDone()).body as AnalyzeOperationOutput;
  return (result.analyzeResult?.pages || []).map((page: any) => ({
    page_content: page.words?.map((line: any) => line.content).join(' ') || '',
    metadata: {
      page: page.pageNumber,
    },
  }));
}

/**
 * 获取PDF文件页数，按照 embeddingParallelCount 分成多个数组
 * @param totalPage PDF文件总页数
 * @param embeddingParallelCount 并行处理的数量
 * @param embeddingBatchSize 每个并行处理的批次大小
 * @returns 按照 embeddingParallelCount 分成多个数组的页数
 */
export async function getPdfPageChunks(
  totalPage: number,
  embeddingParallelCount: number,
  embeddingBatchSize: number,
) {
  const chunkSize = Math.ceil(totalPage / embeddingParallelCount);
  const pageChunks = Array.from({ length: embeddingBatchSize }, (_, i) => {
    const start = i * chunkSize;
    const end = Math.min(start + chunkSize, totalPage);
    return Array.from({ length: end - start }, (_unused, j: number) => start + j + 1);
  }).filter((chunk) => chunk.length > 0);
  return pageChunks;
}

/**
 * 获取文件页面文档
 * 过滤掉字数少于 minTextLength 的数据
 * @param pdfDocsPages OCR识别的PDF文件的所有页面
 * @param pdfPageChunks 按照 embeddingParallelCount 分成多个数组的页数
 * @param minTextLength 最小文本长度
 * @param fileId 文件ID
 * @param personaId 人设ID
 * @param userId 用户ID
 * @returns 过滤掉字数少于 minTextLength 的数据
 */
export async function getFilePageDocs(
  pdfDocsPages: OcrDoc[],
  pdfPageChunks: number[][],
  minTextLength: number,
  fileId: string,
  personaId: string,
  userId: string,
) {
  const pageResult = await Promise.all(
    pdfPageChunks.map(async (pageChunk) => {
      const pageDocs = await Promise.all(
        pageChunk.map(async (pageNumber) => {
          const pageData = pdfDocsPages[pageNumber - 1];
          if (!pageData) {
            return null;
          }
          if (pageData.page_content.length < minTextLength) {
            return null;
          }
          const { page } = pageData.metadata;
          // 生成更短的ID以符合Milvus 50字符限制
          // 使用文件ID的MD5哈希值前8位 + 页码
          const fileIdHash = createHash('md5').update(fileId).digest('hex').substring(0, 8);
          const shortId = `${fileIdHash}_p${page}`;

          return {
            id: shortId,
            page,
            fileId,
            personaId,
            userId,
            content: pageData.page_content || '',
          };
        }),
      );
      return pageDocs.filter(Boolean); // 返回并过滤掉 null
    }),
  );
  const allPageDocs = pageResult.flat(); // 得到最终的扁平文档数组
  return allPageDocs;
}
