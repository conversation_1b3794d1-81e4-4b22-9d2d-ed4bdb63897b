import { createLogger, format, transports } from 'winston';

const logger = createLogger({
  level: 'info',
  format: format.combine(
    format.timestamp(), // 添加时间戳
    format.errors({ stack: true }), // 包含错误堆栈
    format.printf(({ timestamp, level, message, stack, ...meta }) => {
      let logMessage = `${timestamp} ${level}: ${message}`;
      if (stack) {
        logMessage += `\n${stack}`;
      } else if (Object.keys(meta).length > 0) {
        logMessage += ` ${JSON.stringify(meta, null, 2)}`;
      }
      return logMessage;
    }),
  ),
  transports: [new transports.Console()],
});

export default logger;
