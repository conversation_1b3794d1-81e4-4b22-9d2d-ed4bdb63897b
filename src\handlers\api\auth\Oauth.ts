import 'dotenv/config';
import { v4 as uuidv4 } from 'uuid';
import { generateAccessToken } from '@shared/utils/auth';
import { createUser, getUserByEmail } from '@lib/user';
import { log } from '@shared/utils/structuredLogger';

interface OAuthPayload {
  email: string;
  name: string;
}

interface OAuthResponse {
  success: boolean;
  token?: string;
  userId?: string;
  user?: {
    email: string;
    name: string;
  };
  message?: string;
}

const oauthHandler = async (event: OAuthPayload): Promise<OAuthResponse> => {
  const requestId = `oauth-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  try {
    const { email, name } = event;

    if (!email) {
      return {
        success: false,
        message: '邮箱不能为空',
      };
    }

    log.businessEvent('OAUTH_LOGIN_ATTEMPT', undefined, { email, requestId });

    // 查找或创建用户
    const existedUser = await getUserByEmail(email);
    let userId = '';

    if (!existedUser) {
      // 创建新用户
      userId = uuidv4();
      await createUser({
        userId,
        email,
        name,
      });

      log.businessEvent('OAUTH_USER_CREATED', userId, { email, name, requestId });
    } else {
      const { userId: existingUserId } = existedUser;
      userId = existingUserId;
      log.businessEvent('OAUTH_USER_FOUND', userId, { email, requestId });
    }

    // 生成访问令牌
    const token = await generateAccessToken(userId);
    if (!token) {
      log.error('OAuth token generation failed', new Error('Token generation failed'), {
        userId,
        email,
        requestId,
      });

      return {
        success: false,
        message: '令牌生成失败',
      };
    }

    log.businessEvent('OAUTH_LOGIN_SUCCESS', userId, { email, requestId });

    return {
      success: true,
      token,
      userId,
      user: {
        email,
        name,
      },
    };
  } catch (error) {
    log.error('OAuth login failed', error as Error, {
      email: event.email,
      requestId,
    });

    return {
      success: false,
      message: 'OAuth登录失败',
    };
  }
};

export const handler = oauthHandler;
