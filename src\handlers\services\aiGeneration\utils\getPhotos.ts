import { getPhotosByUserAndPersona } from '@infrastructure/database/db/photo';

export const getPhotos = async (userId: string, personaId: string) => {
  try {
    const result = await getPhotosByUserAndPersona(userId, personaId);
    const availablePhotos = result.photos.filter((photo) => !photo.isUse);
    if (availablePhotos.length === 0) {
      return [];
      // throw new Error('没有可用的照片进行分析');
    }
    // 检查照片是否已有分析结果，过滤出有有效summary的照片
    const photosWithSummary = availablePhotos.filter((photo) => {
      try {
        // 检查summary是否是有效的JSON格式
        if (photo.summary && photo.summary.trim().startsWith('{')) {
          const parsed = JSON.parse(photo.summary);
          return parsed.content && parsed.tags; // 确保有content和tags字段
        }
        return false;
      } catch (error) {
        return false;
      }
    });
    return photosWithSummary;
  } catch (error) {
    console.error('获取照片信息失败', error);
    throw error;
  }
};
