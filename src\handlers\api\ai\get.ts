import 'dotenv/config';
import { APIGatewayEvent, Handler } from 'aws-lambda';
import { checkAuth } from '@shared/utils/auth';
import { ResponseWrapper } from '@shared/utils/response';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { globalErrorHandler } from '@shared/middleware/errorHandler';
import { log } from '@shared/utils/structuredLogger';
import { validateRequest } from '@shared/middleware/validation';
import { getConfigByUserAndPersona } from '@lib/config';

// 定义获取配置验证规则
const getConfigValidationSchema = {
  personaId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 100,
  },
};

const getHandler = async (event: APIGatewayEvent) => {
  const requestId = getRequestId(event);
  // 身份验证
  const userId = checkAuth(event);

  try {
    log.businessEvent('CONFIG_GET_STARTED', userId, {
      requestId,
    });

    const validator = validateRequest(getConfigValidationSchema);
    const { personaId } = validator(event);

    // 获取配置
    const config = await getConfigByUserAndPersona(userId, personaId);
    if (!config) {
      log.businessEvent('CONFIG_NOT_FOUND', userId, {
        requestId,
        personaId,
      });

      return ResponseWrapper.error(
        ErrorCode.PERSONA_NOT_FOUND,
        '配置不存在',
        undefined,
        requestId,
      );
    }

    log.businessEvent('CONFIG_GET_SUCCESS', userId, {
      requestId,
      personaId,
      configId: config.id,
    });

    return ResponseWrapper.success(
      {
        message: '获取配置成功',
        config,
      },
      requestId,
    );
  } catch (error) {
    log.error('Config get failed', error as Error, {
      userId,
      requestId,
    });

    return ResponseWrapper.error(
      ErrorCode.INTERNAL_ERROR,
      `获取配置失败: ${error}`,
      undefined,
      requestId,
    );
  }
};

export const handler: Handler = globalErrorHandler(getHandler);
