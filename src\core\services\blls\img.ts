import * as fs from 'fs/promises';
import { fromBuffer } from 'pdf2pic';

import { IMG_WIDTH } from '@shared/utils/const';
import { fileExists, genReadSignedUrl, getObjectBuffer, uploadFile } from '@shared/utils/s3';

/**
 * 生成 PDF 页面图片
 */
export async function generatePageImage(pdfS3Key: string, pageNum: number): Promise<string> {
  const bucketName = process.env.XHS_SOURCE_BUCKET_NAME;
  if (!bucketName) {
    throw new Error(`Missing env info, bucket ${bucketName || 'empty'}`);
  }
  const pdfBuffer = await getObjectBuffer(bucketName, pdfS3Key);
  if (!pdfBuffer) {
    throw new Error('Failed to read PDF from S3');
  }
  const convert = fromBuffer(pdfBuffer, {
    format: 'png',
    width: IMG_WIDTH,
    density: 100,
    quality: 100,
    preserveAspectRatio: true,
    savePath: '/tmp',
  });

  try {
    const result = await convert(pageNum, {
      responseType: 'image',
    });
    if (!result.path) {
      throw new Error('No path in result');
    }
    const resultBuffer = await fs.readFile(result.path);

    const imageKey = `pages/${pdfS3Key}/${pageNum}.png`;
    // 上传到 S3
    await uploadFile(bucketName, imageKey, resultBuffer);
    // 删除临时文件
    await fs.unlink(result.path);
    return imageKey;
  } catch (error) {
    console.error(`Error processing page ${pageNum}:`, error);
    throw error;
  }
}

/**
 * 获取现有图片
 * @param pages 图片路径 s3Key[]
 * @returns  图片s3路径,如果图片不存在，则返回空字符串
 */
export async function getExistingImages(pages: string[]): Promise<string[]> {
  const bucket = process.env.XHS_SOURCE_BUCKET_NAME;
  if (!bucket) {
    throw new Error('Missing XHS_SOURCE_BUCKET_NAME environment variable');
  }
  const results = await Promise.all(
    pages.map(async (s3Key) => {
      if (await fileExists(bucket, s3Key)) {
        return genReadSignedUrl(bucket, s3Key, 86400);
      }
      return '';
    }),
  );
  return results;
}
