import 'dotenv/config';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ResponseWrapper } from '@shared/utils/response';
import { ApiError } from '@shared/utils/errors';
import { getRequestId } from '@shared/utils/requestUtils';
import { ErrorCode } from '@shared/types/response';
import { log } from '@shared/utils/structuredLogger';

/**
 * 检查是否为开发环境
 */
const isDevelopment = () => (process.env.NODE_ENV || 'development') === 'development';

/**
 * 错误类型映射
 */
const errorTypeMap: Record<string, ErrorCode> = {
  ValidationError: ErrorCode.VALIDATION_ERROR,
  UnauthorizedError: ErrorCode.UNAUTHORIZED,
  NotFoundError: ErrorCode.PDF_NOT_FOUND,
  TimeoutError: ErrorCode.TIMEOUT_ERROR,
  SyntaxError: ErrorCode.VALIDATION_ERROR,
};

/**
 * 全局错误处理中间件
 */
export function globalErrorHandler(
  handler: (event: APIGatewayProxyEvent) => Promise<APIGatewayProxyResult>,
) {
  return async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    const requestId = getRequestId(event);
    const startTime = Date.now();

    try {
      // 记录请求开始
      log.requestStart(requestId, event.httpMethod || 'UNKNOWN', event.path || '/', {
        userAgent: event.headers?.['user-agent'],
        ip: event.requestContext?.identity?.sourceIp,
      });

      // 执行处理函数
      const result = await handler(event);

      // 记录请求成功
      const duration = Date.now() - startTime;
      log.requestComplete(requestId, result.statusCode, duration);

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      // 记录错误详情
      log.requestFailed(requestId, error as Error, duration, {
        method: event.httpMethod,
        path: event.path,
        headers: isDevelopment() ? event.headers : undefined,
        body: isDevelopment() ? event.body : undefined,
      });

      // 处理已知的API错误
      if (error instanceof ApiError) {
        return ResponseWrapper.error(error.errorCode, error.message, error.details, requestId);
      }

      // 处理其他已知错误类型
      const err = error as Error;
      const errorCode = errorTypeMap[err.name];
      if (errorCode) {
        return ResponseWrapper.error(
          errorCode,
          err.message,
          isDevelopment() ? { stack: err.stack } : undefined,
          requestId,
        );
      }

      // 处理AWS SDK错误
      const awsErr = error as any;
      if (awsErr.name?.includes('AWS') || awsErr.$metadata) {
        return ResponseWrapper.error(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'AWS服务错误',
          isDevelopment() ? awsErr.message : undefined,
          requestId,
        );
      }

      // 处理数据库错误
      const dbErr = error as Error;
      if (dbErr.name?.includes('DynamoDB') || dbErr.message?.includes('DynamoDB')) {
        return ResponseWrapper.error(
          ErrorCode.DATABASE_ERROR,
          '数据库操作失败',
          isDevelopment() ? dbErr.message : undefined,
          requestId,
        );
      }

      // 处理超时错误
      const timeoutErr = error as Error;
      if (timeoutErr.name === 'TimeoutError' || timeoutErr.message?.includes('timeout')) {
        return ResponseWrapper.error(ErrorCode.TIMEOUT_ERROR, '请求超时', undefined, requestId);
      }

      // 默认内部错误
      return ResponseWrapper.internalError(
        '服务器内部错误',
        isDevelopment()
          ? {
              message: timeoutErr.message,
              stack: timeoutErr.stack,
            }
          : undefined,
        requestId,
      );
    }
  };
}

/**
 * 异步错误处理装饰器
 */
export function asyncErrorHandler<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      // 重新抛出错误，让上层处理
      console.error('Async error:', error);
      throw error;
    }
  };
}

/**
 * 数据库操作错误处理
 */
export function handleDatabaseError(error: any, operation: string): never {
  log.error(`Database operation failed: ${operation}`, error);

  if (error.name?.includes('ValidationException')) {
    throw new ApiError(ErrorCode.VALIDATION_ERROR, `数据验证失败: ${operation}`);
  }

  if (error.name?.includes('ResourceNotFoundException')) {
    throw new ApiError(ErrorCode.PDF_NOT_FOUND, `资源未找到: ${operation}`);
  }

  if (error.name?.includes('ConditionalCheckFailedException')) {
    throw new ApiError(ErrorCode.DATABASE_ERROR, `数据冲突: ${operation}`);
  }

  throw new ApiError(
    ErrorCode.DATABASE_ERROR,
    `数据库操作失败: ${operation}`,
    isDevelopment() ? error.message : undefined,
  );
}

/**
 * 外部服务错误处理
 */
export function handleExternalServiceError(error: any, serviceName: string): never {
  log.error(`External service error: ${serviceName}`, error);

  // Azure AI 错误
  if (serviceName.includes('Azure')) {
    throw new ApiError(
      ErrorCode.AZURE_AI_ERROR,
      `Azure AI服务错误: ${error.message}`,
      isDevelopment() ? error : undefined,
    );
  }

  // AWS Bedrock 错误
  if (serviceName.includes('Bedrock')) {
    throw new ApiError(
      ErrorCode.BEDROCK_ERROR,
      `AWS Bedrock服务错误: ${error.message}`,
      isDevelopment() ? error : undefined,
    );
  }

  // Milvus 错误
  if (serviceName.includes('Milvus')) {
    throw new ApiError(
      ErrorCode.MILVUS_ERROR,
      `Milvus向量数据库错误: ${error.message}`,
      isDevelopment() ? error : undefined,
    );
  }

  // S3 错误
  if (serviceName.includes('S3')) {
    throw new ApiError(
      ErrorCode.S3_ERROR,
      `S3存储服务错误: ${error.message}`,
      isDevelopment() ? error : undefined,
    );
  }

  // SQS 错误
  if (serviceName.includes('SQS')) {
    throw new ApiError(
      ErrorCode.SQS_ERROR,
      `SQS队列服务错误: ${error.message}`,
      isDevelopment() ? error : undefined,
    );
  }

  // 默认外部服务错误
  throw new ApiError(
    ErrorCode.EXTERNAL_SERVICE_ERROR,
    `外部服务错误: ${serviceName}`,
    isDevelopment() ? error.message : undefined,
  );
}

/**
 * 业务逻辑错误处理
 */
export function handleBusinessError(errorCode: ErrorCode, message: string, details?: any): never {
  throw new ApiError(errorCode, message, details);
}

/**
 * 性能监控装饰器
 */
export function withPerformanceMonitoring(operationName: string) {
  return function performanceDecorator<T extends any[], R>(
    _target: any,
    _propertyName: string,
    descriptor: TypedPropertyDescriptor<(...args: T) => Promise<R>>,
  ) {
    const method = descriptor.value!;

    return {
      ...descriptor,
      value: async function performanceWrapper(...args: T): Promise<R> {
        const startTime = Date.now();

        try {
          const result = await method.apply(this, args);
          const duration = Date.now() - startTime;

          console.log(`Performance: ${operationName}`, {
            duration: `${duration}ms`,
            success: true,
            timestamp: new Date().toISOString(),
          });

          return result;
        } catch (error) {
          const duration = Date.now() - startTime;

          const err = error as Error;
          console.log(`Performance: ${operationName}`, {
            duration: `${duration}ms`,
            success: false,
            error: err.message,
            timestamp: new Date().toISOString(),
          });

          throw error;
        }
      },
    };
  };
}
