/* eslint-disable no-await-in-loop */
import {
  BedrockRuntimeClient,
  InvokeModelCommand,
  ConverseCommand,
  InvokeModelWithResponseStreamCommand,
} from '@aws-sdk/client-bedrock-runtime';
import { sendMessageToClient } from '../../core/services/websocket';

export type MessagesForClaude = {
  role: 'user' | 'assistant';
  content: (
    | {
        type: 'text';
        text: string;
      }
    | {
        type: 'image';
        source: {
          type: 'base64';
          media_type: string;
          data: string;
        };
      }
  )[];
}[];

// 新的 Converse API 消息类型
export type ConverseMessage = {
  role: 'user' | 'assistant';
  content: (
    | {
        text: string;
      }
    | {
        image: {
          format: 'png' | 'jpeg' | 'gif' | 'webp';
          source: {
            bytes: Uint8Array;
          };
        };
      }
  )[];
};

// Converse API 推理配置
export type InferenceConfig = {
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  stopSequences?: string[];
};

// 思考过程配置
export type ThinkingConfig = {
  type: 'enabled';
  budget_tokens: number;
};

// Messages API 消息类型（支持思考过程）
export type MessagesAPIMessage = {
  role: 'user' | 'assistant';
  content: (
    | {
        type: 'text';
        text: string;
      }
    | {
        type: 'thinking';
        thinking: string;
        signature?: string;
      }
    | {
        type: 'image';
        source: {
          type: 'base64';
          media_type: string;
          data: string;
        };
      }
  )[];
};

// Messages API 请求配置
export type MessagesAPIConfig = {
  anthropic_version: string;
  max_tokens: number;
  temperature?: number;
  top_p?: number;
  system?: string;
  thinking?: ThinkingConfig;
  anthropic_beta?: string[];
};

// 流式传输事件类型
export type StreamEvent =
  | {
      type: 'message_start';
      message: {
        id: string;
        type: 'message';
        role: 'assistant';
        content: any[];
        model: string;
        stop_reason: string | null;
        stop_sequence: string | null;
      };
    }
  | {
      type: 'content_block_start';
      index: number;
      content_block: {
        type: 'thinking' | 'text';
        thinking?: string;
        text?: string;
      };
    }
  | {
      type: 'content_block_delta';
      index: number;
      delta: {
        type: 'thinking_delta' | 'text_delta' | 'signature_delta';
        thinking?: string;
        text?: string;
        signature?: string;
      };
    }
  | {
      type: 'content_block_stop';
      index: number;
    }
  | {
      type: 'message_delta';
      delta: {
        stop_reason: string | null;
        stop_sequence: string | null;
      };
    }
  | {
      type: 'message_stop';
    };

// 流式传输回调函数类型
export type StreamCallback = (event: StreamEvent) => void | Promise<void>;

// Claude思考过程WebSocket消息类型
export type ClaudeThinkingMessage = {
  type: 'thinking' | 'text' | 'error' | 'complete' | 'start';
  content?: string;
  index?: number;
  error?: string;
  message?: string;
  taskId?: string;
  usage?: {
    input_tokens: number;
    output_tokens: number;
  };
};

// 兼容现有WebSocket服务的消息格式
export type WebSocketMessage = {
  type: string;
  connectionId: string;
  data: any;
};

export async function invokeModelByBedrock(
  model: string,
  payload: {
    anthropic_version: string;
    max_tokens: number;
    temperature: number;
    messages: any[];
    system?: string;
  },
) {
  const client = new BedrockRuntimeClient({ region: 'us-east-1' });
  const command = new InvokeModelCommand({
    contentType: 'application/json',
    accept: 'application/json',
    body: JSON.stringify(payload),
    modelId: model,
  });
  const apiResponse = await client.send(command);
  const decodedResponseBody = new TextDecoder().decode(apiResponse.body);
  const responseBody = JSON.parse(decodedResponseBody);
  return responseBody.content[0].text;
}

/**
 * 使用新的 Converse API 调用 Bedrock 模型
 * @param modelId 模型ID
 * @param messages 消息数组
 * @param inferenceConfig 推理配置
 * @param region AWS 区域
 * @returns 模型响应文本
 */
export async function converseWithBedrock(
  modelId: string,
  messages: ConverseMessage[],
  inferenceConfig?: InferenceConfig,
): Promise<string> {
  const client = new BedrockRuntimeClient({
    region: 'us-east-1',
  });

  const input = {
    modelId,
    messages,
    inferenceConfig: {
      maxTokens: 1000,
      temperature: 0.7,
      ...inferenceConfig,
    },
  };

  const command = new ConverseCommand(input);
  const response = await client.send(command);

  return response.output?.message?.content?.[0]?.text || '';
}

/**
 * 创建文本消息
 * @param text 文本内容
 * @returns 文本消息对象
 */
export function createTextMessage(text: string): ConverseMessage {
  return {
    role: 'user',
    content: [{ text }],
  };
}

/**
 * 创建包含图片的消息
 * @param text 文本内容
 * @param imageBuffer 图片数据
 * @param format 图片格式
 * @returns 包含图片的消息对象
 */
export function createImageMessage(
  text: string,
  imageBuffer: ArrayBuffer,
  format: 'png' | 'jpeg' | 'gif' | 'webp',
): ConverseMessage {
  return {
    role: 'user',
    content: [
      { text },
      {
        image: {
          format,
          source: {
            bytes: new Uint8Array(imageBuffer),
          },
        },
      },
    ],
  };
}

/**
 * 使用 Messages API 调用支持思考过程的 Claude 4 模型
 * @param modelId 模型ID (如: anthropic.claude-sonnet-4-20250514-v1:0)
 * @param messages 消息数组
 * @param config API配置
 * @param region AWS 区域
 * @returns 模型响应
 */
export async function invokeClaudeWithThinking(
  modelId: string,
  messages: MessagesAPIMessage[],
  config: MessagesAPIConfig,
): Promise<{
  content: Array<{
    type: 'text' | 'thinking';
    text?: string;
    thinking?: string;
    signature?: string;
  }>;
  usage?: {
    input_tokens: number;
    output_tokens: number;
  };
}> {
  const client = new BedrockRuntimeClient({
    region: 'us-east-1',
  });

  const payload = {
    ...config,
    messages,
  };

  const command = new InvokeModelCommand({
    contentType: 'application/json',
    accept: 'application/json',
    body: JSON.stringify(payload),
    modelId,
  });

  const apiResponse = await client.send(command);
  const decodedResponseBody = new TextDecoder().decode(apiResponse.body);
  const responseBody = JSON.parse(decodedResponseBody);

  return responseBody;
}

/**
 * 创建思考过程配置
 * @param budgetTokens 思考过程的token预算 (最小1024)
 * @returns 思考配置对象
 */
export function createThinkingConfig(budgetTokens: number = 4000): ThinkingConfig {
  if (budgetTokens < 1024) {
    throw new Error('思考过程的token预算不能少于1024');
  }
  return {
    type: 'enabled',
    budget_tokens: budgetTokens,
  };
}

/**
 * 创建 Messages API 配置
 * @param maxTokens 最大输出token数
 * @param options 其他配置选项
 * @returns Messages API 配置对象
 */
export function createMessagesAPIConfig(
  maxTokens: number,
  options: {
    temperature?: number;
    top_p?: number;
    system?: string;
    thinking?: ThinkingConfig;
    anthropic_version?: string;
    anthropic_beta?: string[];
  } = {},
): MessagesAPIConfig {
  return {
    anthropic_version: options.anthropic_version || 'bedrock-2023-05-31',
    max_tokens: maxTokens,
    temperature: options.temperature,
    top_p: options.top_p,
    system: options.system,
    thinking: options.thinking,
    anthropic_beta: options.anthropic_beta,
  };
}

/**
 * 创建 Messages API 文本消息
 * @param text 文本内容
 * @param role 消息角色
 * @returns Messages API 消息对象
 */
export function createMessagesAPITextMessage(
  text: string,
  role: 'user' | 'assistant' = 'user',
): MessagesAPIMessage {
  return {
    role,
    content: [{ type: 'text', text }],
  };
}

/**
 * 使用流式传输调用支持思考过程的 Claude 4 模型
 * @param modelId 模型ID
 * @param messages 消息数组
 * @param config API配置
 * @param onStream 流式传输回调函数
 * @param region AWS 区域
 * @returns Promise<void>
 */
export async function invokeClaudeWithThinkingStream(
  modelId: string,
  messages: MessagesAPIMessage[],
  config: MessagesAPIConfig,
  onStream: StreamCallback,
): Promise<void> {
  const client = new BedrockRuntimeClient({
    region: 'us-east-1',
  });

  const payload = {
    ...config,
    messages,
  };

  const command = new InvokeModelWithResponseStreamCommand({
    contentType: 'application/json',
    accept: 'application/json',
    body: JSON.stringify(payload),
    modelId,
  });

  try {
    const response = await client.send(command);

    if (!response.body) {
      throw new Error('No response body received');
    }

    // 创建流处理器
    const streamProcessor = async (stream: any) => {
      const reader = stream[Symbol.asyncIterator]();
      let result = await reader.next();
      while (!result.done) {
        const chunk = result.value;
        if (chunk.chunk?.bytes) {
          const chunkStr = new TextDecoder().decode(chunk.chunk.bytes);

          // 直接尝试解析整个chunk作为JSON（Claude流数据格式）
          try {
            const data = JSON.parse(chunkStr.trim());
            console.log('stream data=====>', data);
            await onStream(data);
          } catch (parseError) {
            // 如果整个chunk不是JSON，尝试按行处理
            const lines = chunkStr.split('\n');
            const processLines = lines
              .filter((line) => line.trim().length > 0) // 过滤空行
              .map(async (line) => {
                try {
                  let data;

                  // 尝试不同的格式
                  if (line.startsWith('data: ')) {
                    // SSE格式: data: {...}
                    data = JSON.parse(line.slice(6));
                  } else if (line.trim().startsWith('{')) {
                    // 直接JSON格式: {...}
                    data = JSON.parse(line.trim());
                  } else {
                    // 其他格式，跳过
                    return;
                  }

                  console.log('stream data=====>', data);
                  await onStream(data);
                } catch (lineParseError) {
                  console.warn('Failed to parse line:', lineParseError, 'Line:', line);
                }
              });

            await Promise.all(processLines);
          }
        }
        result = await reader.next();
      }
    };

    await streamProcessor(response.body);
  } catch (error) {
    console.error('Streaming error:', error);
    throw error;
  }
}

/**
 * 发送Claude思考消息到WebSocket客户端（使用现有的WebSocket服务）
 * @param connectionId WebSocket连接ID
 * @param message Claude思考消息
 * @returns Promise<void>
 */
export async function sendClaudeMessageToClient(
  connectionId: string,
  message: ClaudeThinkingMessage,
): Promise<void> {
  const websocketMessage: WebSocketMessage = {
    type: 'search',
    connectionId,
    data: message,
  };
  console.log('connectionId', connectionId);
  console.log('websocketMessage', JSON.stringify(websocketMessage));
  await sendMessageToClient(connectionId, websocketMessage);
}

/**
 * 创建WebSocket友好的流式传输处理器（使用现有WebSocket服务）
 * @param connectionId WebSocket连接ID
 * @param options 配置选项
 * @returns 流式传输回调函数
 */
export function createClaudeWebSocketStreamHandler(
  connectionId: string,
  taskId: string,
  options: {
    sendThinking?: boolean;
    sendProgress?: boolean;
    bufferSize?: number;
  } = {},
): StreamCallback {
  const { sendThinking = true, sendProgress = true, bufferSize = 100 } = options;

  let thinkingBuffer = '';
  let textBuffer = '';
  let currentIndex = -1;
  console.log('connectionId', connectionId);
  return async (event: StreamEvent) => {
    try {
      switch (event.type) {
        case 'message_start':
          if (sendProgress) {
            await sendClaudeMessageToClient(connectionId, {
              taskId,
              type: 'start',
              message: 'Claude开始思考...',
            });
          }
          break;

        case 'content_block_start':
          currentIndex = event.index;
          if (event.content_block.type === 'thinking' && sendThinking) {
            await sendClaudeMessageToClient(connectionId, {
              taskId,
              type: 'thinking',
              content: '',
              index: currentIndex,
            });
          } else if (event.content_block.type === 'text') {
            await sendClaudeMessageToClient(connectionId, {
              taskId,
              type: 'text',
              content: '',
              index: currentIndex,
            });
          }
          break;

        case 'content_block_delta':
          if (event.delta.type === 'thinking_delta' && sendThinking) {
            thinkingBuffer += event.delta.thinking || '';

            // 按缓冲区大小发送思考内容
            if (thinkingBuffer.length >= bufferSize) {
              await sendClaudeMessageToClient(connectionId, {
                taskId,
                type: 'thinking',
                content: event.delta.thinking,
                index: event.index,
              });
              thinkingBuffer = '';
            }
          } else if (event.delta.type === 'text_delta') {
            textBuffer += event.delta.text || '';

            // 按缓冲区大小发送文本内容
            if (textBuffer.length >= bufferSize) {
              await sendClaudeMessageToClient(connectionId, {
                taskId,
                type: 'text',
                content: event.delta.text,
                index: event.index,
              });
              textBuffer = '';
            }
          }
          break;

        case 'content_block_stop':
          // 发送剩余的缓冲内容
          if (thinkingBuffer && sendThinking) {
            await sendClaudeMessageToClient(connectionId, {
              taskId,
              type: 'thinking',
              content: thinkingBuffer,
              index: event.index,
            });
            thinkingBuffer = '';
          }

          if (textBuffer) {
            await sendClaudeMessageToClient(connectionId, {
              taskId,
              type: 'text',
              content: textBuffer,
              index: event.index,
            });
            textBuffer = '';
          }
          break;

        case 'message_stop':
          await sendClaudeMessageToClient(connectionId, {
            taskId,
            type: 'complete',
          });
          break;

        default:
          // 处理其他事件类型
          break;
      }
    } catch (error) {
      console.error('WebSocket handler error:', error);
      await sendClaudeMessageToClient(connectionId, {
        taskId,
        type: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };
}
