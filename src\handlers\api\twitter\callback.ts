import { APIGatewayProxyEventV2, APIGatewayProxyResultV2 } from 'aws-lambda';

import { DeleteCommand, GetCommand } from '@aws-sdk/lib-dynamodb';
import { getDBClient } from '@lib/init';
import { saveTwitterSession } from '@lib/twitterSession';
import { TwitterOAuthService } from '@shared/utils/twitter/auth';
import { EncryptionService } from '@shared/utils/twitter/encryption';
import { TwitterManager } from '@shared/utils/twitter/manager';

const dbClient = getDBClient();
const TEMP_AUTH_TABLE = 'XHS_TWITTER_AUTH_SESSIONS';

export const handler = async (event: APIGatewayProxyEventV2): Promise<APIGatewayProxyResultV2> => {
  try {
    const { code, state, error } = event.queryStringParameters || {};

    const frontendDomain = process.env.FRONTEND_DOMAIN || 'https://your-frontend-domain.com';

    // 检查OAuth错误
    if (error) {
      return {
        statusCode: 302,
        headers: {
          Location: `${frontendDomain}/twitter-auth?error=${error}`,
        },
      };
    }

    if (!code || !state) {
      return {
        statusCode: 302,
        headers: {
          Location: `${frontendDomain}/twitter-auth?error=missing_params`,
        },
      };
    }

    // 从DynamoDB获取授权会话数据
    const sessionResult = await dbClient.send(
      new GetCommand({
        TableName: TEMP_AUTH_TABLE,
        Key: { sessionId: state },
      }),
    );

    const sessionData = sessionResult.Item;

    if (!sessionData || sessionData.state !== state) {
      return {
        statusCode: 302,
        headers: {
          Location: `${frontendDomain}/twitter-auth?error=invalid_state`,
        },
      };
    }

    // 检查会话是否过期
    if (new Date() > new Date(sessionData.expiresAt)) {
      return {
        statusCode: 302,
        headers: {
          Location: `${frontendDomain}/twitter-auth?error=session_expired`,
        },
      };
    }

    // 初始化Twitter OAuth服务
    const twitterOAuth = new TwitterOAuthService({
      clientId: process.env.TWITTER_CLIENT_ID!,
      clientSecret: process.env.TWITTER_CLIENT_SECRET!,
      redirectUri: process.env.TWITTER_REDIRECT_URI!,
      scopes: ['tweet.read', 'tweet.write', 'users.read', 'offline.access'],
    });

    // 交换授权码获取tokens
    const tokens = await twitterOAuth.exchangeCodeForTokens(code, sessionData.codeVerifier);

    // 获取用户信息
    const twitterManager = new TwitterManager({
      clientId: process.env.TWITTER_CLIENT_ID!,
      clientSecret: process.env.TWITTER_CLIENT_SECRET!,
      redirectUri: process.env.TWITTER_REDIRECT_URI!,
      scopes: ['tweet.read', 'tweet.write', 'users.read', 'offline.access'],
    });

    const userInfo = await twitterManager.getUserInfo(tokens);

    // 加密token后存储到DynamoDB
    const encryption = new EncryptionService();
    await saveTwitterSession({
      userId: sessionData.userId,
      accessToken: await encryption.encrypt(tokens.accessToken),
      refreshToken: tokens.refreshToken ? await encryption.encrypt(tokens.refreshToken) : '',
      expiresAt: tokens.expiresAt?.toISOString() || '',
      twitterUserId: userInfo?.id || '',
      twitterUsername: userInfo?.username || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });

    // 清理临时会话数据
    await dbClient.send(
      new DeleteCommand({
        TableName: TEMP_AUTH_TABLE,
        Key: { sessionId: state },
      }),
    );

    return {
      statusCode: 302,
      headers: {
        Location: `${frontendDomain}/twitter-auth?success=authorized`,
      },
    };
  } catch (error) {
    console.error('Twitter callback error:', error);
    const frontendDomain = process.env.FRONTEND_DOMAIN || 'https://your-frontend-domain.com';
    return {
      statusCode: 302,
      headers: {
        Location: `${frontendDomain}/twitter-auth?error=auth_failed&message=${encodeURIComponent(error instanceof Error ? error.message : 'Unknown error')}`,
      },
    };
  }
};
